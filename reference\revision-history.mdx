---
title: 文档修订记录
sidebar_label: 文档修订记录
---

## 2023 年 11 月 8 日

- 修复 [emitCustomSystemEvent](../reference/device-app-api/newAPI/app/emitCustomSystemEvent.mdx) 参数错误
- 修复 [requestPermission](../reference/device-app-api/newAPI/app/requestPermission.mdx) 参数错误
- 补充 [注册设置应用](../guides/framework/app-settings/register.md) 示例

## 2023 年 10 月 20 日

- 全新 [快速起步](../guides/quick-start.mdx)
- 新增 [社区贡献](../guides/community.md)

## 2023 年 9 月 1 日

- 发布 Zepp OS 3.0 相关内容，版本更新内容查看 [Zepp OS 3.0 新特性](../guides/version-info/new-features-30.md)

## 2023 年 8 月 22 日

- 新增 [`PAGE_INDICATOR`](../reference/device-app-api/newAPI/ui/widget/PAGE_INDICATOR.mdx) 控件，页面指示器

## 2023 年 8 月 1 日

- `widget.setProperty` 新增 `DATA_SET` 控件自定义属性
- 修复 `readdirSync` 代码示例错误

## 2023 年 7 月 6 日

- `DIALOG` 控件修复字段描述错误
- `CHECKBOX_GROUP` 控件修复描述错误

## 2023 年 5 月 17 日

- `BUTTON` 控件新增 `long_press` 属性
- `IMG_ANIM` 控件新增 `step` 属性
- `CYCLE_LIST` 控件新增 `item_focus_change_func` 属性
- `CYCLE_IMAGE_TEXT_LIST` 控件新增 `item_focus_change_func` 属性
- `POLYLINE` 控件新增渐变色和平滑曲线属性
- `ARC` 控件新增文字说明，描述绘制逻辑

## 2023 年 5 月 12 日

- 修复 `TEXT` 控件示例错误
- 补充表盘 `data_type` 能力

## 2023 年 4 月 25 日

- 更新 Zepp OS 2.1 版本相关内容，参考 [Zepp OS 2.1 新特性](https://docs.zepp.com/zh-cn/docs/v2/guides/version-info/new-features-21/)

## 2023 年 4 月 21 日

- 修复 POLYLINE 示例代码错误

## 2023 年 4 月 13 日

- 修复表盘示例链接错误
- 修复 Wear 传感器示例错误

## 2023 年 4 月 6 日

- 新增 fetch 兼容性处理

## 2023 年 3 月 23 日

- 重写副屏应用和快捷卡片文档 [注册副屏应用和快捷卡片](../guides/framework/device/secondary-widget.md)

## 2023 年 3 月 10 日

- 控件动画新增动画状态 [控件动画](../reference/device-app-api/newAPI/ui/widgetAnimations.mdx)
- 模拟器新增数字表冠模拟说明 [模拟器使用说明](../guides/tools/simulator/index.md#键盘模拟真机实体按键)

## 2023 年 3 月 3 日

- 新增最佳实践 [错误捕获](../guides/best-practice/error-catch.mdx)

## 2023 年 2 月 24 日

- 新增 showcase 示例小程序 [Showcase](../samples/app/showcase.md)
- 修复 `onKey`、`onDigitalCrown` 错误
- 补充 Zepp OS 文档平台版本说明
- 修复 IMG_CLICK 示例代码注释错误

## 2023 年 1 月 29 日

- 修复 createModal 错误
- 补充 Sleep updateInfo 方法

## 2023 年 1 月 5 日

- 补充 sensor 和 fs 模块说明
- 补充模拟器键盘模拟按键说明 [模拟器使用说明](../guides/tools/simulator/index.md#键盘模拟真机实体按键)

## 2022 年 12 月 16 日

- 补充应用介绍截图和 icon 规范 [发布应用](../distribute/index.md#应用介绍截图)

## 2022 年 11 月 25 日

- 完善 [SCROLL_LIST](../reference/device-app-api/newAPI/ui/widget/SCROLL_LIST.mdx) 说明
- 修复 [POLYLINE 折线图](../reference/device-app-api/newAPI/ui/widget/GRADIENT_POLYLINE.mdx) 示例代码错误

## 2022 年 11 月 9 日

- 新增 [POLYLINE 折线图](../reference/device-app-api/newAPI/ui/widget/GRADIENT_POLYLINE.mdx)

## 2022 年 10 月 28 日

- 新增 [小程序文件系统介绍](../guides/framework/device/fs.md)
- 新增 [选择合适的 API 版本](https://docs.zepp.com/zh-cn/docs/v2/guides/version-info/version-choose/)

## 2022 年 10 月 21 日

- 新增 [小程序调试技巧](../guides/best-practice/debug.mdx)
- 补充 [快速上手](../guides/quick-start.mdx) 相关内容
- 补充 [基础环境搭建](../guides/best-practice/Basic-environment-construction.mdx)
  - 新增 Windows 平台安装 nvm 说明
- 重写 [Zeus CLI 使用说明](../guides/tools/cli/index.md)
- 重写 [模拟器使用说明](../guides/tools/simulator/index.md)
- 重写 [模拟器安装和启动](../guides/tools/simulator/setup.md)
- 修复 Settings App Button 组件属性错误
- 修复 Settings App Text 组件属性错误
- 修复 `setScrollMode` 代码示例错误
- 修复心率传感器代码示例错误

## 2022 年 10 月 13 日

- [app.json](../reference/app-json.mdx)
  - 补充小程序 icon 相关信息
  - 修复 secondaryWidget 配置错误
- 新增 [快速上手](../guides/quick-start.mdx)
- 新增 [控件动画](../reference/device-app-api/newAPI/ui/widgetAnimations.mdx)
- [设备信息](../reference/related-resources/device-list.mdx) 补充 Amazfit Falcon 相关信息

## 2022 年 10 月 10 日

- 新增 Zepp OS 2.0 文档版本切换功能
- 新增 Zepp OS 2.0 专栏，介绍版本新特性，详情参考 [Zepp OS 2.0 新特性](../guides/version-info/new-features.md)
- 全新设备应用 API，详情参考 [TEXT](../reference/device-app-api/newAPI/ui/widget/TEXT.mdx)
- 更新最佳实践栏目为 2.0 版本，详情参考 [数据持久化](../guides/best-practice/persistence-storage.mdx)
- 更新小程序代码示例为 2.0 版本，参考 [Calories](../samples/app/calories.md)

## 2022 年 9 月 15 日

- `deleteWidget`
  - 补充代码用例
- `CYCLE_IMAGE_TEXT_LIST`
  - 补充图片示例
  - 补充代码用例

## 2022 年 8 月 22 日

- 修复 [多语言映射](../reference/related-resources/language-list.mdx) 部分语言代码缩写错误
- 新增 `QRCODE` 控件，支持二维码展示
- `IMG` 控件
  - 优化字段描述
  - 优化图片示例

## 2022 年 8 月 19 日

- `Fetch API`
  - 修复文档错误，补充基本用法示例

## 2022 年 8 月 12 日

- `alarmNew`
  - 补充 `param` 参数
- `RADIO_GROUP` 控件
  - 完善 `prop` 属性描述
- `TEXT` 控件
  - 删除无用 `TEXT_STYLE` 属性

## 2022 年 7 月 27 日

- `VIBRATE` 传感器
  - 重新整理 `scene` 参数
  - 删除无用属性

## 2022 年 7 月 21 日

- `IMG` 控件
  - 补充支持图片类型
- 补充设置应用 [框架接口](../reference/app-settings-api/global.mdx)
- 补充伴生服务 [框架接口](../reference/side-service-api/global.mdx)
- [整体架构](../guides/architecture/arc.mdx)
  - 补充蓝牙通信相关说明
- 新增最佳实践 [蓝牙通信](../guides/best-practice/bluetooth-communication.mdx)
- hmBle
  - 补充蓝牙通信说明
- Messaging API
  - 补充蓝牙通信说明

## 2022 年 7 月 15 日

- Settings Storage API
  - 补充 `length` 属性
  - 补充 `toObject` 方法
  - 优化描述
- Fetch API
  - 优化方法描述

## 2022 年 7 月 12 日

- `widget.addEventListener`
  - 优化方法描述
- `hmUI.createDialog`
  - 补充图片示例
  - 新增 `auto_hide` 属性
  - 重写代码示例
- `hmUI.createWidget`
  - 优化方法描述
- `hmUI.deleteWidget`
  - 优化方法描述
- `widget.getProperty`
  - 优化方法描述
- `hmUI.getScrollCurrentPage`
  - 优化方法描述
- `widget.getType`
  - 优化方法描述
- `widget.removeEventListener`
  - 优化方法描述
- `hmUI.scrollToPage`
  - 优化方法描述
- `widget.setProperty`
  - 优化方法描述
- `hmUI.setScrollView`
  - 补充图片示例
  - 新增 `isVertical` 属性
  - 重写代码示例
- `hmUI.showToast`
  - 补充图片示例
  - 优化方法描述
- [小程序配置](./app-json.mdx)
  - 补充 targets 对象描述
- [目录结构](./../guides/architecture/folder-structure.mdx)
  - 补充 assets 目录描述

## 2022 年 7 月 6 日

- `SCROLL_LIST` 控件
  - 补充控件图片展示
  - 重写代码示例
- `CYCLE_LIST` 控件
  - 补充控件图片展示
  - 重写代码示例
- `PICK_DATE` 控件
  - 补充控件图片展示
  - 重写代码示例
- `HISTOGRAM` 控件
  - 补充控件图片展示
  - 重写代码示例
- `FILL_RECT` 控件
  - 补充注意事项

## 2022 年 7 月 1 日

- `IMG` 控件
  - 补充控件图片展示
  - 补充 `src` 字段描述
  - 重写代码示例
- `TEXT` 控件
  - 补充控件图片展示
  - 重写代码示例
- `ARC` 控件
  - 补充控件图片展示
  - 优化字段描述
  - 重写代码示例
- `FILL_RECT` 控件
  - 补充控件图片展示
  - 删除无用字段 `line_width`
  - 重写代码示例
- `STROKE_RECT` 控件
  - 补充控件图片展示
  - 重写代码示例
  - 修复字段 `line_width` 必填错误
- `TEXT_IMG`
  - 移动至表盘专用控件
- `ARC_PROGRESS`
  - 移动至表盘专用控件
- `BUTTON`
  - 补充控件图片展示
  - 优化字段描述
  - 补充使用注意事项
  - 重写代码示例
- `IMG_LEVEL`
  - 移动至表盘专用控件
- `IMG_AMIN`
  - 补充控件图片展示
  - 删除无用字段 `anim_repeat`
  - 优化字段 `anim_status` 描述
  - 重写代码示例
- `CIRCLE`
  - 补充控件图片展示
  - 重写代码示例
- `RADIO_GROUP`
  - 补充控件图片展示
  - 优化字段描述
  - 补充使用注意事项
  - 重写代码示例
- `CHECKBOX_GROUP`
  - 补充控件图片展示
  - 优化字段描述
  - 补充使用注意事项
  - 重写代码示例
- `SLIDE_SWITCH`
  - 补充控件图片展示
  - 优化字段描述
  - 重写代码示例
- `DIALOG`
  - 补充控件图片展示
  - 补充控件使用注意事项
  - 重写代码示例
- `hmFS.stat_asset`
  - 修复字段描述错误
- `hmFS.open_asset`
  - 修复字段描述错误

## 2022 年 6 月 23 日

- `CYCLE_IMAGE_TEXT_LIST` 控件
  - 修复代码示例错误

## 2022 年 6 月 21 日

- `ARC_PROGRESS` 控件
  - 修复属性圆心 y 字段名错误
- `RADIO_GROUP` 控件
  - 修复代码示例错误
- `CHECKBOX_GROUP` 控件
  - 修复代码示例错误

## 2022 年 6 月 17 日

- `IMG` 控件
  - 补充 `IMG` 控件暂不支持缩放的说明

## 2022 年 6 月 14 日

- `widget.addEventListener`
  - 修复用例错误

## 2022 年 6 月 9 日

- `widget.getProperty`
  - 补充属性获取说明
- `hmApp.registerGestureEvent(callback)`
  - 补充默认行为的说明
- `hmApp.goBack()`
  - 修改方法描述

## 2022 年 6 月 1 日

- `hm.stat`
  - 修复 `stat` 参数类型错误
- `hm.stat_asset`
  - 修复 `stat` 参数类型错误

## 2022 年 5 月 31 日

- `hmSetting.setBrightScreen`
  - 新增屏幕常亮的描述
- `hmApp.setScreenKeep`
  - 新增描述
- `CALORIE`
  - 补充属性说明
- `FAT_BURRING`
  - 补充属性说明
- `VIBRATE`
  - 补充传感器 `start`、`stop` 方法说明和代码示例
- `HEART`
  - 修复拼写错误

## 2022 年 5 月 27 日

- `hmApp.registerKeyEvent`
  - 修复错误描述
- `hmUI.getTextLayout`
  - 补充 `wrapped` 字段，新增获取单行文本宽度用法
- `SCROLL_LIST` 控件
  - 修复代码用例错误
- `IMG_TIME`
  - 补充 `minute_follow`、`second_follow` 字段
- 新增 [实体按键](./related-resources/physical-keys.mdx)
- [搭载 Zepp OS 设备列表](./related-resources/device-list.mdx)中补充了各设备的按键数量
- [多语言映射](./related-resources/language-list.mdx) 新增 8 国语言

## 2022 年 5 月 24 日

- [屏幕适配](../guides/best-practice/multi-screen-adaption.mdx)中新增状态栏相关
- [搭载 Zepp OS 设备列表](./related-resources/device-list.mdx)中补充了各设备的屏幕形状和尺寸

## 2022 年 5 月 17 日

- `SPO2`
  - 修复描述错误

## 2022 年 4 月 28 日

- `hmApp.setLayerY`
  - 补充描述
- `hmApp.getLayerY`
  - 补充描述

## 2022 年 4 月 12 日

- `hmApp.startApp`
  - 修复 `param` 参数类型，补充字段说明
- `hmApp.gotoPage`
  - 修复 `param` 参数类型，补充字段说明
- `hmApp.reloadPage`
  - 修复 `param` 参数类型，补充字段说明
- 补充生命周期函数签名

## 2022 年 4 月 11 日

- 修复小程序配置 app.json 中 `permissions`、`cover`、`platforms` 字段类型

## 2022 年 3 月 31 日

- `hmSetting.getDeviceInfo`
  - 修复返回值 `keyNumber` 的类型
  - 补充 `screenShape` 字段说明

## 2022 年 3 月 29 日

- `hmApp.packageInfo`
  - 补充返回值信息
- `hmApp.registSpinEvent`
  - 方法名修改为 `hmApp.registerSpinEvent`，原方法名仍然可以使用
- `hmApp.registGestureEvent`
  - 方法名修改为 `hmApp.registerGestureEvent`，原方法名仍然可以使用
- `hmApp.registKeyEvent`
  - 方法名修改为 `hmApp.registerKeyEvent`，原方法名仍然可以使用

## 2022 年 3 月 22 日

- `IMG_ANIM` 控件
  - 修复 `anim_prefix`、`anim_ext` 属性类型
  - 完善代码用例配套的文件系统存放路径
- `hmUI.getProperty`
  - 删除错误代码用例

## 2022 年 3 月 18 日

- 新增 `hmUI.getTextLayout()`，预获取 `TEXT` 控件高度
- `hmSetting.getUserData`
  - 返回值新增 `region` 字段
- `TEXT`
  - 修改 `text_size` 属性描述
  - 删除 `font` 属性，暂不支持设置字体

## 2022 年 2 月 24 日

- 新增 `PICK_DATE` 日期选择控件
- `TIME` 时间传感器
  - 新增中国农历节日、节气相关 API
- 新增 `hmSetting.getTimeFormat()` 获取时间格式
- `FILL_RECT` 控件
  - 新增 `line_width` 属性，支持设置描边宽度
- 新增搭载 Zepp OS 设备列表及其代号文档
- `IMG` 控件
  - 补充图片旋转和位置相关的图片示例
- `stopTimer`
  - 修复代码用例中的错误
- `WEATHER` 天气传感器
  - 重写此模块文档
  - 新增日出、日落数据
- `hmFS.readdir`
  - 修复返回值的类型
  - 补充代码用例
- `hmFS.stat`
  - 完善 `mtime` 属性的描述
- `hmFS.stat_asset`
  - 完善 `mtime` 属性的描述
- `hmApp.exit`
  - 完善描述
- `hmApp.gotoHome`
  - 完善描述

## 2022 年 1 月 17 日

- `hmUI.createDialog`
  - 修复 `show` 参数类型为 `boolean`
- `widget.setProperty`
  - 补充同时设置多个属性的用例
- `widget.getProperty`
  - 补充同时获取多个属性的用例

## 2021 年 12 月 28 日

- `hmSetting.getDeviceInfo`
  - 返回值新增 `deviceSource` 字段
- `hmFS.write`
  - 修复文档参数传递顺序错误
- `hmFS.read`
  - 修复文档参数传递顺序错误
- `hmSetting.getMileageUnit`
  - 修复文档方法名标注错误
- `FAT_BURRING` 脂肪燃烧传感器
  - 修复文档示例代码错误
- `BATTERY` 电量传感器
  - 修复文档示例代码错误
- `PAI` 传感器
  - 修复文档属性说明错误
- `hmSetting.getUserData()`
  - 修复 `age` 属性类型为 `number`
- `hmSetting.getWeightTarget()`
  - 修复文档错误
- `Select`
  - 补充 `options` 类型
