---
title: Geolocation
sidebar_label: Geolocation 定位
---

> API_LEVEL `2.1` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

定位传感器。

:::info
权限代码： `device:os.geolocation`
:::

## 方法

### start

开始监听定位数据

```ts
start(): void
```

### stop

停止监听定位数据

```ts
stop(): void
```

### getStatus

获取定位状态，返回 `A` 代表定位中，返回 `V` 代表无效定位

```ts
getStatus(): string
```

### getLatitude

获取纬度

```ts
getLatitude(option: Option): Result
```

#### Option

| 属性   | 类型                | 必填 | 默认值          | 说明                                                  | API_LEVEL |
| ------ | ------------------- | ---- | --------------- | ----------------------------------------------------- | --------- |
| format | <code>string</code> | 否   | <code>DD</code> | 坐标格式，可选 `DD` 代表十进制或者 `DMS` 度分秒的形式 | 2.1       |

#### Result

| 类型                         | 说明                    |
| ---------------------------- | ----------------------- |
| <code>number&#124;DMS</code> | 坐标，坐标系类型 WGS-84 |

#### DMS

| 属性      | 类型                | 说明                             | API_LEVEL |
| --------- | ------------------- | -------------------------------- | --------- |
| direction | <code>string</code> | 方向，`N` 代表北纬，`S` 代表南纬 | 2.1       |
| degrees   | <code>number</code> | 度                               | 2.1       |
| minutes   | <code>number</code> | 分                               | 2.1       |
| seconds   | <code>number</code> | 秒                               | 2.1       |

### getLongitude

获取经度

```ts
getLongitude(option: Option): Result
```

#### Option

| 属性   | 类型                | 必填 | 默认值          | 说明                                                  | API_LEVEL |
| ------ | ------------------- | ---- | --------------- | ----------------------------------------------------- | --------- |
| format | <code>string</code> | 否   | <code>DD</code> | 坐标格式，可选 `DD` 代表十进制或者 `DMS` 度分秒的形式 | 2.1       |

#### Result

| 类型                         | 说明                    |
| ---------------------------- | ----------------------- |
| <code>number&#124;DMS</code> | 坐标，坐标系类型 WGS-84 |

#### DMS

| 属性      | 类型                | 说明                             | API_LEVEL |
| --------- | ------------------- | -------------------------------- | --------- |
| direction | <code>string</code> | 方向，`E` 代表东经，`W` 代表西经 | 2.1       |
| degrees   | <code>number</code> | 度                               | 2.1       |
| minutes   | <code>number</code> | 分                               | 2.1       |
| seconds   | <code>number</code> | 秒                               | 2.1       |

### getSetting

> API_LEVEL `3.0`

获取定位设置

```ts
getSetting(): Result
```

#### Result

| 属性 | 类型                | 说明                            | API_LEVEL |
| ---- | ------------------- | ------------------------------- | --------- |
| mode | <code>number</code> | 定位设置，值描述详见下方 `mode` | 3.0       |

#### mode

| 值  | 类型                | 说明         | API_LEVEL |
| --- | ------------------- | ------------ | --------- |
| 0   | <code>number</code> | 精准模式     | 3.0       |
| 1   | <code>number</code> | 智能模式     | 3.0       |
| 2   | <code>number</code> | 均衡模式     | 3.0       |
| 3   | <code>number</code> | 省电模式     | 3.0       |
| 4   | <code>number</code> | 超级省电模式 | 3.0       |
| 5   | <code>number</code> | 自定义模式   | 3.0       |

### onChange

注册定位信息变化事件监听回调函数

```ts
onChange(callback: () => void): void
```

### offChange

取消定位信息变化事件监听回调函数

```ts
offChange(callback: () => void): void
```

### onGnssChange

> API_LEVEL `3.0`

注册 GNSS 信息变化事件监听回调函数

```ts
onGnssChange(callback: (info: Info) => void): void
```

#### Info

| 属性               | 类型                                        | 说明                                         | API_LEVEL |
| ------------------ | ------------------------------------------- | -------------------------------------------- | --------- |
| agps_inject_time   | <code>number</code>                         | AGPS 更新时间 UTC 时间戳，单位毫秒           | 3.0       |
| top4_cn_val        | <code>number</code>                         | 定位卫星的信号强度值                         | 3.0       |
| is_dualband        | <code>number</code>                         | 是否双频                                     | 3.0       |
| nb_valid_satellite | <code>number</code>                         | 可用卫星数量                                 | 3.0       |
| nb_used_satellite  | <code>number</code>                         | 使用的卫星数量                               | 3.0       |
| elapsed_time       | <code>number</code>                         | 从搜索卫星开始到定位成功所消耗的时间，单位秒 | 3.0       |
| satellite_data     | <code>Array&#60;SatelliteSystem&#62;</code> | 卫星数据数组                                 | 3.0       |

#### SatelliteSystem

| 属性               | 类型                                  | 说明                                | API_LEVEL |
| ------------------ | ------------------------------------- | ----------------------------------- | --------- |
| gnss_id            | <code>number</code>                   | 卫星系统 ID，值描述见下方 `gnss_id` | 3.0       |
| sub_top4_cn_val    | <code>number</code>                   | 该卫星系统的最强信号值              | 3.0       |
| nb_valid_satellite | <code>number</code>                   | 搜索到可用卫星数量                  | 3.0       |
| gsv_data           | <code>Array&#60;Satellite&#62;</code> | 单颗卫星数据数组，最大长度 32       | 3.6       |

#### gnss_id

| 值  | 类型                | 说明                       | API_LEVEL |
| --- | ------------------- | -------------------------- | --------- |
| 0   | <code>number</code> | GPS 全球定位系统           | 3.0       |
| 1   | <code>number</code> | BDS 北斗卫星导航系统       | 3.0       |
| 2   | <code>number</code> | GLONASS 全球导航卫星系统   | 3.0       |
| 3   | <code>number</code> | GALILEO 伽利略定位系统     | 3.0       |
| 4   | <code>number</code> | QZSS 准天顶卫星系统        | 3.0       |
| 5   | <code>number</code> | IRNSS 印度区域导航卫星系统 | 3.0       |

#### Satellite

| 属性      | 类型                | 说明    | API_LEVEL |
| --------- | ------------------- | ------- | --------- |
| id        | <code>number</code> | 卫星 ID | 3.6       |
| elevation | <code>number</code> | 俯仰角  | 3.6       |
| azimuth   | <code>number</code> | 方位角  | 3.6       |
| snr       | <code>number</code> | 信噪比  | 3.6       |

### offGnssChange

> API_LEVEL `3.0`

取消 GNSS 信息变化事件监听回调函数

```ts
offGnssChange(callback: (info: Geolocation.onGnssChange.Info) => void): void
```

### getEnabled

> API_LEVEL `4.0`

获取用户是否允许小程序使用定位功能

```ts
getEnabled(): boolean
```

### onEnableChange

> API_LEVEL `4.0`

注册用户定位授权状态变化事件监听回调函数

```ts
onEnableChange(callback: () => void): void
```

### offEnableChange

> API_LEVEL `4.0`

取消用户定位授权状态变化事件监听回调函数

```ts
offEnableChange(callback: () => void): void
```

## 代码示例

```js
import { Geolocation } from '@zos/sensor'

const geolocation = new Geolocation()

const callback = () => {
  if (geolocation.getStatus() === 'A') {
    console.log(geolocation.getLatitude())
    console.log(geolocation.getLongitude())
  }
}

geolocation.start()
geolocation.onChange(callback)

// When not needed for use
geolocation.offChange(callback)
geolocation.stop()
```
