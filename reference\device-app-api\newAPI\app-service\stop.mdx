# stop

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

关闭指定的设备应用服务，异步调用，关闭结果通过回调函数返回。

:::info
权限代码： `device:os.bg_service`
:::

## 类型

```ts
function stop(option: Option): Result
```

## 参数

### Option

| 属性          | 类型                                                      | 必填 | 默认值 | 说明                                                                   | API_LEVEL |
| ------------- | --------------------------------------------------------- | ---- | ------ | ---------------------------------------------------------------------- | --------- |
| file          | <code>string</code>                                       | 是   | -      | 设备应用服务 js 文件，必须是在 app.json 中 service module 中配置的文件 | 3.0       |
| complete_func | <code>(callbackOption: CallbackOption) =&#62; void</code> | 是   | -      | 设备应用服务关闭完成回调函数                                           | 3.0       |

### CallbackOption

| 属性   | 类型                 | 说明                                                    | API_LEVEL |
| ------ | -------------------- | ------------------------------------------------------- | --------- |
| file   | <code>string</code>  | 设备应用服务 js 文件，与 `stop` 传入参数相同            | 3.0       |
| result | <code>boolean</code> | 设备应用服务关闭结果，`true` 代表成功，`false` 代表失败 | 3.0       |

### Result

| 类型                 | 说明                                    |
| -------------------- | --------------------------------------- |
| <code>boolean</code> | 如果返回 `0` 则表明设备应用服务关闭成功 |

## 代码示例

```js
import { stop } from '@zos/app-service'
```
