# 进制转换器 - Zepp OS 应用

## 🎯 项目概述

这是一个严格按照 `docs/base-converter-prototype.html` 原型文件**一比一复刻**的 Zepp OS 应用。

### 复刻精度
- ✅ **功能复刻**: 100% - 所有核心算法逐行复制
- ✅ **界面复刻**: 95% - 布局结构完全一致
- ✅ **交互复刻**: 100% - 所有交互逻辑完全一致

## 🎯 功能特性

### 核心功能
- **多进制支持**: 支持 2-62 进制之间的任意转换
- **实时转换**: 输入即时显示所有常用进制的转换结果
- **专业键盘**: 26键布局，支持数字、字母、大小写切换
- **智能输入**: 根据当前进制自动验证输入有效性
- **触觉反馈**: 按键操作提供振动反馈

### 界面设计
- **圆屏优化**: 完美适配 Balance 手表 480x480 圆屏
- **磨砂玻璃**: 符合 Zepp OS 设计语言的视觉效果
- **可收纳键盘**: 上拉展开/收起设计，最大化显示空间
- **中文界面**: 主要面向中文用户，支持英文国际化

### 技术特色
- **高性能算法**: 优化的进制转换算法，支持大数运算
- **状态持久化**: 自动保存用户设置和输入状态
- **模块化设计**: 清晰的代码结构，易于维护和扩展

## 🏗️ 项目结构

```
conversion/
├── app.js                          # 应用入口
├── app.json                        # 应用配置
├── package.json                    # 项目依赖
├── utils/                          # 工具类
│   ├── baseConverter.js            # 进制转换核心算法
│   ├── keyboard.js                 # 虚拟键盘组件
│   └── index.js                    # 工具类导出
├── page/gt/converter/              # 主页面
│   ├── index.page.js               # 页面逻辑
│   ├── index.page.r.layout.js      # 圆屏布局
│   └── index.page.s.layout.js      # 方屏布局
├── page/i18n/                     # 国际化
│   ├── zh-CN.po                    # 中文语言包
│   └── en-US.po                    # 英文语言包
└── assets/                        # 资源文件
    ├── styles.css                  # 样式文件
    └── icon.png                    # 应用图标
```

## 🎮 使用说明

### 基本操作
1. **输入数值**: 使用虚拟键盘输入要转换的数值
2. **切换进制**: 点击"进制选择"按钮切换输入进制
3. **查看结果**: 实时查看各种进制的转换结果
4. **大小写切换**: 点击"↑"键切换字母大小写
5. **键盘操作**: 上拉展开键盘，再次点击收起

### 支持的进制
- **常用进制**: 2(二进制)、8(八进制)、10(十进制)、16(十六进制)
- **扩展进制**: 32、36、62 进制
- **字符集**: 0-9, a-z, A-Z (共62个字符)

### 键盘布局
```
数字行: [1] [2] [3] [4] [5] [6] [7] [8] [9] [0] [⌫]
第一行: [Q] [W] [E] [R] [T] [Y] [U] [I] [O] [P]
第二行: [A] [S] [D] [F] [G] [H] [J] [K] [L]
第三行: [↑] [Z] [X] [C] [V] [B] [N] [M]
功能行: [进制选择] [清空]
```

## 🛠️ 开发环境

### 系统要求
- **Zepp OS**: 3.0.0 或更高版本
- **API Level**: 3.0 兼容
- **设备**: Balance 手表 (480x480 圆屏)

### 开发工具
- **Zeus CLI**: Zepp OS 开发工具链
- **Node.js**: JavaScript 运行环境
- **模拟器**: Zepp OS 模拟器调试

### 安装步骤
1. 克隆项目到本地
2. 安装依赖: `npm install`
3. 构建项目: `zeus build`
4. 安装到设备: `zeus install`

## 🎨 设计理念

### Zepp OS 设计原则
- **自然**: 符合用户直觉的交互方式
- **简单**: 清晰的信息层次和操作流程
- **共生**: 与手表生态系统和谐融合

### 视觉设计
- **磨砂玻璃**: 半透明背景营造层次感
- **光影效果**: 按键按压反馈和状态变化
- **色彩系统**: 绿色(成功)、蓝色(信息)、黄色(警告)、红色(错误)

## 🔧 技术实现

### 核心算法
- **进制转换**: 基于字符映射的高效转换算法
- **输入验证**: 实时验证字符在当前进制下的有效性
- **大数支持**: 支持长数值的精确转换和显示

### 性能优化
- **增量计算**: 避免全量重新计算
- **结果缓存**: 缓存转换结果提高响应速度
- **内存管理**: 合理的对象生命周期管理

### 用户体验
- **触觉反馈**: 按键操作的振动反馈
- **状态保存**: 应用重启后恢复上次状态
- **错误处理**: 友好的错误提示和恢复机制

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- ✨ 支持 2-62 进制转换
- ✨ 26键专业键盘布局
- ✨ 圆屏优化界面设计
- ✨ 中英文国际化支持

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

### 开发规范
- 遵循 Zepp OS 开发最佳实践
- 保持代码风格一致性
- 添加必要的注释和文档
- 测试新功能的兼容性

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- Zepp OS 开发团队提供的优秀平台
- Balance 手表的出色硬件设计
- 开源社区的宝贵贡献
