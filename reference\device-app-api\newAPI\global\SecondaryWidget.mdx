# SecondaryWidget

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册副屏应用，指定当前页面的生命周期回调等。每个副屏应用都必须调用 `SecondaryWidget()` 构造函数且只能调用一次。

## 类型

```ts
function SecondaryWidget(option: Option): Result
```

## 参数

### Option

| 属性      | 类型                                       | 必填 | 默认值 | 说明                                                              | API_LEVEL |
| --------- | ------------------------------------------ | ---- | ------ | ----------------------------------------------------------------- | --------- |
| state     | <code>object</code>                        | 否   | -      | SecondaryWidget 副屏应用实例上挂载的数据对象，可用于存储状态      | 2.0       |
| onInit    | <code>(params?: string) =&#62; void</code> | 否   | -      | 初始化完成时触发，只触发一次，可以用来初始化 SecondaryWidget 状态 | 2.0       |
| build     | <code>(params?: string) =&#62; void</code> | 否   | -      | 在 `onInit` 执行完成后触发，推荐在 `build` 生命周期中进行 UI 绘制 | 2.0       |
| onResume  | <code>() =&#62; void</code>                | 否   | -      | 当屏幕焦点聚焦在此副屏应用上时触发                                | 2.0       |
| onPause   | <code>() =&#62; void</code>                | 否   | -      | 当屏幕焦点离开此副屏应用上时触发                                  | 2.0       |
| onDestroy | <code>() =&#62; void</code>                | 否   | -      | 销毁时触发 `onDestroy` 生命周期函数                               | 2.0       |

### Result

| 类型                 | 说明                 |
| -------------------- | -------------------- |
| <code>unknown</code> | SecondaryWidget 实例 |

## 代码示例

```js title="secondaryWidget.js"
SecondaryWidget({
  state: {
    text: 'Hello Zepp OS',
  },
  onInit() {
    console.log('onInit')
  },
  build() {
    console.log('build')
    console.log(this.state.text)
  },
})
```
