# DataWidget

> API_LEVEL `3.6` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册应用插件，指定当前页面的生命周期回调等。每个应用插件都必须调用 `DataWidget()` 构造函数且只能调用一次。

## 类型

```ts
function DataWidget(option: Option): Result
```

## 参数

### Option

| 属性      | 类型                                       | 必填 | 默认值 | 说明                                                              | API_LEVEL |
| --------- | ------------------------------------------ | ---- | ------ | ----------------------------------------------------------------- | --------- |
| state     | <code>object</code>                        | 否   | -      | DataWidget 实例上挂载的数据对象，可用于存储状态                   | 3.6       |
| onInit    | <code>(params?: string) =&#62; void</code> | 否   | -      | 初始化完成时触发，只触发一次，可以用来初始化应用插件状态          | 3.6       |
| build     | <code>(params?: string) =&#62; void</code> | 否   | -      | 在 `onInit` 执行完成后触发，推荐在 `build` 生命周期中进行 UI 绘制 | 3.6       |
| onResume  | <code>() =&#62; void</code>                | 否   | -      | 当屏幕焦点聚焦在此应用插件上时触发                                | 3.6       |
| onPause   | <code>() =&#62; void</code>                | 否   | -      | 当屏幕焦点离开此应用插件上时触发                                  | 3.6       |
| onDestroy | <code>() =&#62; void</code>                | 否   | -      | 销毁时触发 `onDestroy` 生命周期函数                               | 3.6       |

### Result

| 类型                | 说明            |
| ------------------- | --------------- |
| <code>object</code> | DataWidget 实例 |

## 代码示例

```js title="DataWidget.js"
DataWidget({
  state: {
    text: 'Hello Zepp OS',
  },
  onInit() {
    console.log('onInit')
  },
  build() {
    console.log('build')
    console.log(this.state.text)
  },
})
```
