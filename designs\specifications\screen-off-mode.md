---
sidebar_label: 息屏模式
---

# 息屏模式

![Design](/img/design/5e29e1059b16b9b1b3bf9e5ef39aee20.png)

息屏模式是指表盘在息屏状态下可以有限地显示时间等重要信息，帮助用户在不抬腕情况下的信息获取，它是一个表盘的必备模式；

息屏模式的显示分为两类，跟随当前表盘的息屏和用户可单独选择设置的表盘；跟随当前表盘的息屏需要表盘设计者单独设计；

## 设计规则

- 尽量减少表盘元素，亮起像素不超过屏幕的10%。
- 表盘指针不要秒针。
- 表盘底色必须为黑（\#000000），指针主色必须为白（\#FFFFFF）。
- 保持指针/数字切图尺寸一致。
- 由于要在息屏与亮屏切换，所以尽量减少元素移动，避免切换时的跳动。
- 为保证辨识度，保留基本的时间刻度（非强制）。
- 除时间外，其他元素取舍优先级为：步数\>日期\>天气\>电量\>心率\>其他（非强制但基本遵守）。

## 指针处理方法

指针剪影/数字剪影 + 外边缘黑色描边。

- 去掉渐变、高光、阴影等质感类的参数，保留指针/数字的剪影图形。
- 指针增加外边缘1\~2px的黑色描边，作用是：区分指针的上下层关系，区分指针与数据的上下层关系。
- 可根据表盘特点增加指针颜色，但尽量要少。

![Design](/img/design/763748931d32ca2dd3c0ab46d8a02dd9.png)

白膜 + 外边缘黑色描边。

- 指针保留部分结构性光影，整体为“白膜”效果。
- 指针增加外边缘1\~2px的黑色描边，作用是：区分指针的上下层关系,区分指针与数据的上下层关系。

![Design](/img/design/da796ff32d75059eb0861379683cc9e1.png)

## 数字时间处理方法

- 数字时间一般图形面积较大，所以采用外轮廓描边处理，描边宽度不少于2px。
- 描边颜色选取明度较高的白色或彩色，当亮起面积小于10%时，则可以不使用描边处理。

![Design](/img/design/90cc5ba0-f61b-4db9-ab75-d42bc6d52955.png)
