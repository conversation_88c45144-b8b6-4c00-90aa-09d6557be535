{"configVersion": "v3", "app": {"appId": 25064, "appName": "进制转换器", "appType": "app", "version": {"code": 1, "name": "1.0.0"}, "icon": "icon.png", "vender": "zepp", "description": "支持2-62进制转换的专业计算工具"}, "permissions": ["data:os.device.info", "device:os.local_storage"], "runtime": {"apiVersion": {"compatible": "3.0.0", "target": "3.0.0", "minVersion": "3.0"}}, "debug": false, "i18n": {"en-US": {"appName": "Base Converter"}, "zh-CN": {"appName": "进制转换器"}}, "defaultLanguage": "zh-CN", "module": {"page": {"pages": ["page/gt/converter/index.page"]}}, "designWidth": 480, "platforms": [{"deviceSource": 8519936, "st": "r"}, {"deviceSource": 8519937, "st": "r"}, {"deviceSource": 8519939, "st": "r"}], "largeAppIcon": {"icon": "icon.png", "size": [248, 0], "type": "appIcon"}, "packageInfo": {"mode": "development", "timeStamp": 1758609957, "expiredTime": 172800, "zpm": "3.2.3"}}