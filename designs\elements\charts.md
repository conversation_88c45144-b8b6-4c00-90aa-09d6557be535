---
sidebar_label: 图表
---

# 图表  

图表是由图形、图像、数字等元素组成的，用于解释、呈现目标数据之间的关系和属性。

## 构成  

图表是由：①刻度轴、②数据展示、③竖向网格线、④时间轴组成。可根据实际情况展示元素。  

![Design](/img/design/4fe477652ae0078d28998bd0e536ec1d.png)

## 柱状图  

用于比较两个或以上的价值（不同时间或者不同条件），只有一个变量，通常利用于较小的数据集分析展示。  

请根据不同的时间颗粒度选择最合适的时间轴。

- 24根柱子  

![Design](/img/design/24-column-histograms_1.png)

![Design](/img/design/24-column-histograms_2.png)

- 7根柱子  
![Design](/img/design/f951c3a742cc32c68cc3b9d9b90db051.png)

![Design](/img/design/7317c66c1fd199b7aeeae3d430453d01.png)

- 柱子的宽度和高度可以根据需要调整，但柱子之间应该有很明显的区隔。  

![Design](/img/design/17ac7fe1b2bd79970551d9984f1c14e2.png)

## 使用规则  

### 图表基本使用规范  

- 时间轴的数值在有网格线的情况下第一个数据和最后一个数据与两边线对齐。  

![Design](/img/design/basic-specifications_1.png)

- 不同时间制式下时间轴应显示对应的时间格式（SC：简体中文、TC：繁体中文、JP：日文）。  

![Design](/img/design/basic-specifications_2.png)

- 图表应根据使用情况改变宽高，整体保持视觉居中，有一定的留白。  

![Design](/img/design/basic-specifications_3.png)

### 具有指定意义的颜色  

在数据的展示中，通常会使用一些颜色来代表某类固定的含义。在使用过程中，此类数据展示以色值定义为基础参考，在保持色相一致的情况下，可通过微调来改善不同设备的屏幕颜色显示不佳的情况。  

- 心率区间  

![Design](/img/design/bec5967a7c40c7a8182d4193afcdc50c.png)

- 最大摄氧量  

![Design](/img/design/cd062e2a48bb61e4d87da045a4e4f4d4.png)

- 训练负荷  

![Design](/img/design/757a374f515b4b4888b67e9d75a05104.png)

- 睡眠  

![Design](/img/design/7030904a101dd11073f0897bbc5f3be1.png)