---
title: widget.addEventListener(eventId, callback)
sidebar_label: addEventListener
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

给 UI 控件注册事件监听器，当触发指定事件时，给定的回调函数就会被执行。

## 类型

```ts
(eventId: EventId, callback: (event: Event) => void) => void
```

## 参数

| 参数    | 说明                               | 类型      |
| ------- | ---------------------------------- | --------- |
| eventId | 事件类型（如：滑动、按下、抬起等） | `EventId` |
| event   | 事件详情，具体参考不同事件         | `object`  |

### EventId

| 值                      | 说明 |
| ----------------------- | ---- |
| `event.MOVE`       | 滑动 |
| `event.CLICK_DOWN` | 按下 |
| `event.CLICK_UP`   | 抬起 |
| `event.MOVE_IN`    | 划入 |
| `event.MOVE_OUT`   | 划出 |

## 代码示例

```js
import { createWidget, widget, event } from '@zos/ui'

const img_bkg = createWidget(widget.IMG)

img_bkg.addEventListener(event.CLICK_DOWN, function (info) {
  //控件注册事件监听
  console.log(info.x)
})
```
