---
sidebar_label: 空页面
---

# 空页面  

空页面是初始状态和无数据状态时，没有内容显示的情况下帮助用户理解发生原因的页面。以及可执行的操作的占位提示。  

## 设计目标  

- 需要有简单且清晰的提示，帮助让用户了解空状态原因，避免产生误解与迷失。
- 给予用户推荐操作提示，告知用户该如何处理。  

## 类型  

- **有操作空页面**：告知用户当前页面状态并通过可操作按钮引导用户解决此状态。  

![Design](/img/design/empty-pages-types_1.png)  

>① 闹钟应用空页面，添加闹钟按钮。
>
>② 压力应用空页面，测量压力按钮。
>
>③ 世界时钟副屏空页面，添加时区按钮。

- **无操作空页面**：仅告知用户当前页面状态或可改变当前状态的操作提示，不支持在当前页面进行操作。  

![Design](/img/design/empty-pages-types_2.png)  

>① 卡包应用空页面，提示连接APP。
>
>② 今日活动应用空页面，展示空数据。
>
>③ 睡眠副屏空页面，提示无数据。

## 使用规则

- **无数据场景**  

用于表示内容区域无数据的场景，由图形（包括插画和图标元素）、“--”符号、提示文本、建议操作元素四类元素组合展示，根据使用场景决定是否提供建议操作。  

![Design](/img/design/24f0a5799b34ad6d1914fbaeea30c0d7.png)

![Design](/img/design/no-data-scenarios_2.png)  

>① 数据项的无数据状态，使用“--”来占位表示。
>
>② 文本用来明确告知用户状态信息或者引导用户进行操作。
>
>③ 图表的无数据样式使用图表的网格线或者指示器背景展示，时间轴和刻度轴是否有数据根据实际图表定义展示元素。
>
>④副屏情况下无数据样式，为帮助用户理解当前页面突出重要内容可使用插图形式

- **引导操作场景**  

用于引导用户进行操作的空页面，由提示文本、插图、建议操作元素三个部分组成，图形表意清晰，提示文本需要描述清晰明确，文本长度不超过一屏。  

![Design](/img/design/guided-operation-scenarios.png)

## 视觉规范  

- 应用初始状态状态插图使用灰色。
- 空页面最多两行文案，需要保证一屏显示。
- 左右留出安全距离。  

![Design](/img/design/80f2fe752bddb666e878bbb9f3809d7d.png)

- 副屏页面的空页面初始状态表示需连接手机应用进行操作，请使用彩色图形样式，为保证用户理解，通常使用该应用图标。  

![Design](/img/design/empty-pages-visual-specifications.png)