# openAssetsSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

同步地打开小程序 `/assets` 目录下的文件，获取文件句柄。

## 类型

```ts
function openAssetsSync(option: Option): Result
```

## 参数

### Option

| 属性 | 类型                | 必填 | 默认值                | 说明                 | API_LEVEL |
| ---- | ------------------- | ---- | --------------------- | -------------------- | --------- |
| path | <code>string</code> | 是   | -                     | 文件路径             | 2.0       |
| flag | <code>number</code> | 否   | <code>O_RDONLY</code> | 值参考文件打开的常量 | 2.0       |

### Result

| 类型                | 说明     |
| ------------------- | -------- |
| <code>number</code> | 文件句柄 |

## 常量

### 文件打开的常量

| 常量       | 说明                                                                       | API_LEVEL |
| ---------- | -------------------------------------------------------------------------- | --------- |
| `O_RDONLY` | 指示打开文件以进行只读访问的标志                                           | 2.0       |
| `O_WRONLY` | 指示打开文件以进行只写访问的标志                                           | 2.0       |
| `O_RDWR`   | 指示打开文件以进行读写访问的标志                                           | 2.0       |
| `O_APPEND` | 指示数据将追加到文件末尾的标志                                             | 2.0       |
| `O_CREAT`  | 如果文件不存在则指示创建文件的标志                                         | 2.0       |
| `O_EXCL`   | 如果设置了 `O_CREAT` 标志并且文件已经存在，则指示打开文件应该失败的标志    | 2.0       |
| `O_TRUNC`  | 标志表示如果文件存在并且该文件被成功打开以进行写访问，则其长度应被截断为零 | 2.0       |

## 代码示例

```js
import { openSync, O_RDONLY } from '@zos/fs'

const fd = openAssetsSync({
  path: 'test.txt',
  flag: O_RDONLY,
})
```
