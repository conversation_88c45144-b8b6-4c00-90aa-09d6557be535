---
title: 表盘制作工具
---

# 常见问题

### Q1: 创建表盘的时候选择单种语言和多种语言有什么区别？

如果表盘只支持单语言，请选择单种语言，如果支持简繁英的请选择多语言。单种语言只需要上传对应的语言切图，多语言需要上传3种语言的切图。

### Q2: 表盘的预览图支持修改吗？

支持修改。进入表盘制作页面，点击左上角表盘名称旁边的编辑图标，即可修改。

### Q3: 主从关系中的独立和跟随是什么意思？

”独立“ 需要单独设定元素的坐标；选择`跟随`后，元素会作为一个整体跟随前一个数据

### Q4: 表盘中的数字资源如何上传？

在数字切图中选择，使用新切图，然后上传数字图片，命名需按照对应的数字顺序命名，0-9的数字切图需要保持尺寸大小一致，否则无法上传

### Q5: 表盘中的数字元素切图都是同样的样式，需要每个都重复传图吗？

不需要，可以选择已上传切图。<br/>
<img src="https://img-testing-cdn.huami.com/20210726/ca82c66d-dd50-4bae-a2bb-e99377fa2c381627279134116.png" alt="" width="302" height="201" />

### Q6: 表盘的旋转中心是指什么？

一般情况是表盘尺寸中心，特殊位置如下图以实际指针旋转坐标为准。 <br/>
<img src="https://img-testing-cdn.huami.com/20210726/4fb710be-ca18-4f71-b251-3b3292beee9d1627279160910.png" alt="" width="211" height="211" />

### Q7: 指针的旋转中心是指什么？

指针切图，需要旋转的锚点坐标。如下图所示 <br/>
<img src="https://img-testing-cdn.huami.com/20210726/ee123fa7-cd78-4c4e-851f-9df560da5d121627279186603.png" alt="" width="50" height="138" />

### Q8: 表盘的数据模块的层级顺序是怎样的？
可以按照实际显示拖拽调节。

### Q9: 圆环半径和圆环宽度的参数应该怎么填？
如下图所示 <br/>
<img src="https://img-testing-cdn.huami.com/20210728/81627458900687.png" alt="" width="290" height="122" />

### Q10：图片进度里面的`单张显示`和`连续显示`的区别是什么？

如下图所示 <br/>
<img src="https://img-testing-cdn.huami.com/20210728/91627458912808.png" alt="" width="266" height="74" />

### Q11: 无数据占位图是什么？

当相应模块数据为空的时候，会显示无数据占位图，在心率、天气、湿度模块中，无数据占位图为必填项。

### Q12: 系统状态的切图怎么做？

系统状态：勿扰、蓝牙、锁定等，如有未开启状态，必须切在背景上，上传开启状态图即可。

### Q13: 导出提示文件过大？
"Watchface is too big", 看到此提示可检查：1.图片进度图条是否过大或多，精简进度条 2.息屏刻度是否细节过于丰富，精简刻度。

### Q14: 扫码安装提示下载失败？

请检查当前表盘制作界面“支持手表”列出的信息中是否包含当前 Zepp App 绑定的手表设备<br/>
<img src="https://img-cdn.huami.com/20220329/4afbb4aaa4f0883e90e60787ea0476d0.png" alt="" width="266" height="74" />

### Q15: 如何配置 Zepp OS 表盘的温度单位图片？

华氏温标与摄氏温标是两大国际主流的计量温度的标准。表盘工具目前暂未支持单独设置摄氏度和华氏度单位。因此，在设计表盘时请将温度单位上传为度“°”, 单位图片高度推荐与数字图片高度相同。<br/>
<img src="https://img-cdn.huami.com/20220629/2e233ec6e6c3cafef4a545aefe010763.png" alt="" width="266" height="74" />

### Q16: 如何配置 Zepp OS 表盘的距离单位图片？

因为距离单位和数值会根据 Zepp App 的单位设置变化，所以不可以将距离单位切到背景图上，距离单位需要分别配置公里(km) 和英里(mi)。<br/>
<img src="https://img-cdn.huami.com/20220629/13ebf67e681b869503efa643843bbd6f.png" alt="" width="266" height="74" />
