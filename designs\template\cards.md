---
sidebar_label: 卡片
---

# 卡片  

卡片是一种承载内容，聚合信息的容器，它包含一种或以一种元素为核心的一小组元素。

## 设计原则  

保持简约和相对独立性，卡片设计需要保持简约并限制内容长度，每张卡片应该只包含重要的信息，并提出一个相关的观点。  

区分主次，善用卡片的层次结构，突出关键内容和信息，引导用户关注最重要的信息或操作，更快速地完成任务。  

让整个卡片都可被点击，更大的触摸和触发范畴是卡片本身的优势所在，用户应当可以点击卡片的任何一个部分来触发其中的内容。

## 构成  

作为一个信息容器，卡片能承载包括文字、富媒体、按钮等所有 UI 元素。

文字：包含标题，副标题和描述文本。  

![Design](/img/design/73e9f5169007382d964b54f78c36bca4.png)  

富媒体：卡片可以包含多样富媒体，以显示图标、图形或者图表。  

![Design](/img/design/4d47e4c3af34595c73b3ebb03cd39fc2.png)  

操作: 包含用于操作的按钮和图标。  

![Design](/img/design/ec679ea1c8898c9d0be6c5be76ac3909.png)

## 卡片滑动  

卡片向左滑动时出现删除操作，按钮高度和卡片高度相同，点击后删除当前卡片。卡片滑动动效效果见：[视觉动效](../visual/animations.md#动效)。

![Design](/img/design/card-swipe.png)

## 视觉规范  

- 单个卡片不超过一屏。
- 卡片四周留出安全距离。
- 卡片之间的间距保持统一。  

![Design](/img/design/3279af92a10ea0b802e9f12d33a257ae.png)

![Design](/img/design/card-visual-specifications_2.png)

- 卡片内的排版不做特殊限制，卡片如有操作区，应和内容有明显区别，卡片四周留出安全距离。
[图片]

## 文本内容显示规则  

文本内容超过一行，且容器允许换行的时候，将文本进行折行显示。超出容器限制后显示”...”  

![Design](/img/design/text-regulation_1.png) 

通知中心信息最多支持 3 行  

>① 内容 1 行，正常显示
>
>② 内容 2 行，正常显示
>
>③ 内容超出 3 行，未展示完全的内容使用”...”显示  

![Design](/img/design/text-regulation_2.png)

快捷卡片标题最多支持 1 行  

>① 内容未超出 1 行，正常显示
>
>② 内容超出 2 行，未展示完全的内容使用”...”显示

卡片显示内容超出容器， 不适合换行，文本信息不能省略的情况下可以进行滚动显示。  

![Design](/img/design/text-regulation_3.png)

闹钟卡片最多支持 1 行  

>① 内容未超出 1 行，正常显示
>
>② 内容超出 1 行，内容超出容器时滚动显示  

![Design](/img/design/text-regulation_4.png)

电话联系人卡片最多支持 1 行  

>① 1 行时未超出，正常显示
>
>② 1 行时已超出，内容超出容器时滚动显示
