---
title: CYCLE_IMAGE_TEXT_LIST
sidebar_label: CYCLE_IMAGE_TEXT_LIST 循环列表（文字+图片）
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../../guides/framework/device/compatibility.md)。

![cycle_image_text_list_sample](/img/api/cycle_image_text_list.jpg)

创建一个可以循环滚动的列表，每一个列表项可以放入一张图片和文字。

## 创建 UI 控件

```js
import { createWidget, widget } from '@zos/ui'

const cycleImageTextList = createWidget(widget.CYCLE_IMAGE_TEXT_LIST, Param)
```

## 类型

### Param: object

| 属性                   | 说明                                      | 是否必须 | 类型                  |
| ---------------------- | ----------------------------------------- | -------- | --------------------- |
| x                      | 控件 x 坐标                         | 是       | `number`              |
| y                      | 控件 y 坐标                               | 是       | `number`              |
| w                      | 控件显示宽度                              | 是       | `number`              |
| h                      | 控件显示高度                              | 是       | `number`              |
| item_image_x           | item 图片 x 坐标 相对坐标                 | 是       | `number`              |
| item_image_y           | item 图片 y 坐标 相对坐标                 | 是       | `number`              |
| item_text_x            | item 字体 x 坐标 相对坐标                 | 是       | `number`              |
| item_text_y            | item 字体 y 坐标 相对坐标                 | 是       | `number`              |
| item_text_size         | item 字体大小                             | 是       | `number`              |
| item_text_color        | item 字体颜色                             | 是       | `number`              |
| item_bg_color          | item 背景色                               | 是       | `number`              |
| item_height            | item 高度                                 | 是       | `number`              |
| data_array             | 数据数组                                  | 是       | `Array<Data>`         |
| data_size              | 数组长度                                  | 是       | `number`              |
| item_text_align_h      | 文字横轴方向 不填默认横向居中             | 否       | `number`              |
| item_text_align_v      | 文字竖轴方向 不填默认竖向居中             | 否       | `number`              |
| item_text_height       | 文本实际显示区域 不填则默认为 item_height | 否       | `number`              |
| item_text_width        | 文本实际显示区域 不填则默认为控件显示宽度 | 否       | `number`              |
| item_image_x           | item 图片 x 坐标，相对坐标                | 否       | `number`              |
| item_image_y           | item 图片 y 坐标，相对坐标                | 否       | `number`              |
| item_click_func        | 点击 item 的回调                          | 否       | `ItemClickFunc`       |
| item_focus_change_func | item 焦点态回调                           | 否       | `ItemFocusChangeFunc` |

### Data: object

| 属性 | 说明     | 是否必填 | 类型     |
| ---- | -------- | -------- | -------- |
| src  | 图片路径 | 否       | `string` |
| text | 文本内容 | 是       | `string` |

### ItemClickFunc: function

```ts
(cycleList: CycleList, index: number) => void
```

| 参数      | 类型     | 备注                       |
| --------- | -------- | -------------------------- |
| cycleList | `object` | cycleList 实例             |
| index     | `number` | 点击的 item 索引 从 0 开始 |

### ItemFocusChangeFunc: function

```ts
(cycleList: CycleList, index: number, isFocus: boolean) => void
```

| 参数      | 说明                      | 类型      |
| --------- | ------------------------- | --------- |
| cycleList | cycleList 实例            | `object`  |
| index     | 失去/获取焦点 item 的索引 | `number`  |
| isFocus   | 是否获取焦点              | `boolean` |

## 设置单个 item 文字的属性

:::caution
设置单个 `item` 文字的属性，控件并不会保存这些信息，通过 `getProperty` 无法获取变更后的值
:::

| 属性            | 说明                | 是否必填 | 类型     |
| --------------- | ------------------- | -------- | -------- |
| index           | item 索引 从 0 开始 | 是       | `number` |
| item_text_color | 文本颜色            | 否       | `number` |
| item_text_size  | 文本大小            | 否       | `number` |

```js
const widget = ...
widget.setProperty(prop.ITEM_MORE,{
  index:0,
  item_text_color:0x2f4988,
  item_text_size:50
})
```

## 刷新 ITEM

- 这里是为 `ITEM_MORE` 这个属性设置的 通过 `ITEM_MORE` 设置属性后 如果想恢复原样 可以调刷新 `ITEM`

```js
widget.setProperty(prop.ITEM_REFRESH, 0) // 0 是 item 索引，从 0 开始
```

## 设置 list 的顶部 item 索引

- 通过 `LIST_TOP` 属性设置 `list` 的顶部 `item` 的索引值，以便将 `list` 显示在指定位置的 `item`

| 字段名 | 说明                | 是否必填 | 类型     |
| ------ | ------------------- | -------- | -------- |
| index  | item 索引 从 0 开始 | 是       | `number` |

## 属性访问支持列表

| 属性名               | setProperty | getProperty | [setter](../gettersetter.mdx) | [getter](../gettersetter.mdx) |
|----------------------|-------------|-------------|-------------------------------|-------------------------------|
| x                   | Y           | Y           | Y                             | Y                             |
| y                   | Y           | Y           | Y                             | Y                             |
| w                   | Y           | Y           | Y                             | Y                             |
| h                   | Y           | Y           | Y                             | Y                             |
| data_array          | N           | N           | N                             | Y                             |
| data_size           | N           | N           | N                             | Y                             |
| item_height         | N           | N           | N                             | Y                             |
| item_bg_color       | N           | N           | N                             | Y                             |
| item_text_color     | N           | N           | N                             | Y                             |
| item_text_size      | N           | N           | N                             | Y                             |
| item_text_x         | N           | N           | N                             | Y                             |
| item_text_y         | N           | N           | N                             | Y                             |
| item_text_width     | N           | N           | N                             | Y                             |
| item_text_height    | N           | N           | N                             | Y                             |
| item_text_align_h   | N           | N           | N                             | Y                             |
| item_text_align_v   | N           | N           | N                             | Y                             |
| item_image_x        | N           | N           | N                             | Y                             |
| item_image_y        | N           | N           | N                             | Y                             |
| item_click_func     | N           | N           | N                             | Y                             |
| item_focus_change_func | N           | N           | Y                             | Y                             |

## 代码示例

```js
import { createWidget, widget, prop } from '@zos/ui'

const data_array = [
  { src: rootPath + 'step/step_num_0.png', text: '1' },
  { src: rootPath + 'step/step_num_1.png', text: '2' },
  { src: rootPath + 'step/step_num_2.png', text: '3' }
]
cycleList = createWidget(widget.CYCLE_IMAGE_TEXT_LIST, {
  x: 0,
  y: 0,
  w: 200,
  h: 400,
  data_array: data_array,
  data_size: 3,
  item_height: 100,
  item_bg_color: 0xffffff,
  item_text_color: 0x000000,
  item_text_x: 10,
  item_text_y: 10,
  item_text_size: 18
})

//获取第一行的索引值
ret = cycleList.getProperty(prop.MORE, {})
console.log(ret.index)
```
