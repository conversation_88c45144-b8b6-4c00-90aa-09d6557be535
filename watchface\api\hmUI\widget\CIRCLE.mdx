---
title: CIRCLE
sidebar_label: CIRCLE 圆形
---

![circle_sample](/img/api/circle_sample.jpg)

绘制一个圆形，支持颜色、透明度等属性。

:::caution
CIRCLE 控件暂时不支持通过 `addEventListener` 注册事件
:::

## 创建 UI 控件

```js
const circle = hmUI.createWidget(hmUI.widget.CIRCLE, Param)
```

## 类型

### Param: object

| 属性     | 备注                      | 是否必须 | 类型     |
| -------- | ------------------------- | -------- | -------- |
| center_x | 圆心 x 坐标               | 是       | `number` |
| center_y | 圆心 y 坐标               | 是       | `number` |
| radius   | 半径                      | 是       | `number` |
| color    | 颜色 16 进制值            | 是       | `number` |
| alpha    | 透明度[0-255]，0 为全透明 | 否       | `number` |

## 代码示例

```js
Page({
  build() {
    const circle = hmUI.createWidget(hmUI.widget.CIRCLE, {
      center_x: 240,
      center_y: 240,
      radius: 120,
      color: 0xfc6950,
      alpha: 200
    })
  }
})
```
