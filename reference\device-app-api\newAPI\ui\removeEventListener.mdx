---
title: widget.removeEventListener(eventId, function)
sidebar_label: removeEventListener
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

删除 UI 控件使用 `widget.addEventListener` 方法注册的事件监听器。

## 类型

```ts
(eventId: EventId, callback) => void
```

## 参数

| 参数     | 说明                               | 类型       |
| -------- | ---------------------------------- | ---------- |
| eventId  | 事件类型（如：滑动、按下、抬起等） | `number`   |
| callback | 注册的回调函数                     | `function` |

### EventId

参考 `addEventListener` 的 `EventId`

## 代码示例

```js
import { createWidget, widget, event } from '@zos/ui'

const img_bkg = createWidget(widget.IMG)
const listenerFunc = (info) => {
  console.log(info.x)
}

img_bkg.addEventListener(event.CLICK_DOWN, listenerFunc)
img_bkg.removeEventListener(event.CLICK_DOWN, listenerFunc)
```
