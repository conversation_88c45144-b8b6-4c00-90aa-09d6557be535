let e=null;function t(){return s()&&n()}function i(){return s()&&o()}function n(){return"undefined"!=typeof hmApp}function o(){return"undefined"!=typeof __$$R$$__}function s(){return n()||o()}e="undefined"!=typeof __$$R$$__?__$$R$$__:()=>{},t()?hmApp.getPackageInfo:i()&&e("@zos/app").getPackageInfo,t()?hmUI:i()&&e("@zos/ui"),t()?hmSetting.getDeviceInfo:i()&&e("@zos/device").getDeviceInfo,t()?"undefined"!=typeof __$$app$$__&&__$$app$$__:i()&&e("@zos/i18n").getText,t()?hmApp.gotoPage:i()&&e("@zos/router").push,t()?hmBle:i()&&e("@zos/ble");let l=null;t()?l=DeviceRuntimeCore.HmLogger:i()?l=e("@zos/utils").log:"undefined"!=typeof messaging&&"undefined"!=typeof Logger&&(l=Logger),e("@zos/utils").EventBus,e("@zos/timer").setTimeout,e("@zos/timer").clearTimeout,s()?l.getLogger("device-message"):l.getLogger("side-message"),l.getLogger("message-builder");const r=e("@zos/ble/TransferFile"),u=(f=new r,{onFile(e){return e?(void 0===f||f.inbox.on("newfile",(function(){const t=f.inbox.getNextFile();e&&e(t)})),this):this},onSideServiceFileFinished(e){return e?(void 0===f||f.inbox.on("file",(function(){const t=f.inbox.getNextFile();e&&e(t)})),this):this},emitFile(){return f.inbox.emit("file"),this},offFile(){return void 0===f||(f.inbox.off("newfile"),f.inbox.off("file")),this},getFile:()=>void 0===f?null:f.inbox.getNextFile(),sendFile(e,t){if(void 0===f)throw new Error("fileTransfer is not available");return f.outbox.enqueueFile(e,t)}});var f;function g({state:e={},onInit:t,onDestroy:i,...n}={}){const o=function(){const{messaging:e}=getApp()._options.globalData;return e}();return{state:e,...n,onInit(...e){this._onCall=this.onCall?.bind(this),this._onRequest=this.onRequest?.bind(this),o.onCall(this._onCall).onRequest(this._onRequest),this.onReceivedFile&&(this._onReceivedFile=this.onReceivedFile?.bind(this),u.onFile(this._onReceivedFile)),t?.apply(this,e)},onDestroy(...e){this._onCall&&o.offOnCall(this._onCall),this._onRequest&&o.offOnRequest(this._onRequest),this._onReceivedFile&&u.offFile(this._onReceivedFile),i?.apply(this,e)},request:e=>o.request(e),httpRequest:e=>o.request({method:"http.request",params:e}),call:e=>o.call(e),sendFile:(e,t)=>u.sendFile(e,t)}}export{g as BasePage};
