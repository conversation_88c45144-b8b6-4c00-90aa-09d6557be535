---
sidebar_label: 状态栏
---

# 状态栏

状态栏显示在屏幕顶部，用于告知用户当前页面位置及部分有用信息，如页面标题、时间和电池电量。

![Design](/img/design/cb69eacfcad272ff8887d69ca1882bfa.png)

## 使用规则

- 状态栏用于告知用户当前所处的页面，非应用页面不显示状态栏。例如：控制中心、通知与快捷卡片。考虑可穿戴设备屏幕尺寸限制，特殊或工具应用结合用户使用场景需求及页面排布的合理性，考虑是否取消显示当前页面顶部状态栏。例如：选择器、计算器页面。

![Design](/img/design/f44e0486493c363ddaf7b2f848baa53d.png)

- 特殊页面（如运动类应用页面），顶部状态栏显示电量信息，告知用户当前设备电量状态。

![Design](/img/design/status-bars_2.png)

- 标题文字信息应简洁清晰，字符超长信息使用“...”结尾省略显示。

![Design](/img/design/6d62986da0e07de6fbcbc6f749ff7611.png)
