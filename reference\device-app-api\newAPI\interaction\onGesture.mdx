# onGesture

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

监听用户手势事件，只允许注册一个事件，如果多次注册会导致上一次注册的事件失效。

## 类型

```ts
function onGesture(option: Option): void
```

### 简化调用方式

```ts
function onGesture(callback: (event: GestureEvent) => PreventDefault): void
```

## 参数

### Option

| 属性     | 类型                                                     | 必填 | 默认值 | 说明             | API_LEVEL |
| -------- | -------------------------------------------------------- | ---- | ------ | ---------------- | --------- |
| callback | <code>(event: GestureEvent) =&#62; PreventDefault</code> | 是   | -      | 手势事件回调函数 | 2.0       |

### GestureEvent

| 类型                | 说明                           |
| ------------------- | ------------------------------ |
| <code>number</code> | 手势事件名，值参考手势事件常量 |

### PreventDefault

| 类型                 | 说明                                                  |
| -------------------- | ----------------------------------------------------- |
| <code>boolean</code> | 是否跳过默认手势行为，`true` - 跳过，`false` - 不跳过 |

## 常量

### 手势事件常量

| 常量            | 说明     | API_LEVEL |
| --------------- | -------- | --------- |
| `GESTURE_UP`    | 手势上滑 | 2.0       |
| `GESTURE_DOWN`  | 手势下滑 | 2.0       |
| `GESTURE_LEFT`  | 手势左滑 | 2.0       |
| `GESTURE_RIGHT` | 手势右滑 | 2.0       |

## 代码示例

```js
import { onGesture, GESTURE_UP } from '@zos/interaction'

onGesture({
  callback: (event) => {
    if (event === GESTURE_UP) {
      console.log('up')
    }
    return true
  },
})
```
