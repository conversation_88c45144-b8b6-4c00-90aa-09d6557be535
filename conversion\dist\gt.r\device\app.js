console.log('current filepath: /app.js');
try {
  (() => {
  
const { beforeAppCreate = () => {}, afterAppCreate = () => {} } = DeviceRuntimeCore.LifeCycle;
beforeAppCreate();
const __$$G$$__ = __$$hmAppManager$$__.currentApp.__globals__.__$$G$$__;

!function (context) {
  with (context) {
    const __$$RQR$$__ = __$$R$$__;
    
__$$app$$__.app = App({
    globalData: {},
    onCreate(options) {
        console.log('app on create invoke');
    },
    onDestroy(options) {
        console.log('app on destroy invoke');
    }
});
;
  }
}.bind(__$$G$$__)(__$$G$$__);
afterAppCreate();;
  })();
} catch(e) {
  
console.log('Mini Program Error', e);
e && e.stack && e.stack.split(/\n/).forEach(i => console.log("error stack", i));
;
/* todo */
}