# renameSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

重命名小程序 `/data` 目录下的文件，将文件从 `oldPath` 重命名为 `newPath`。

## 类型

```ts
function renameSync(option: Option): Result
```

## 参数

### Option

| 属性    | 类型                | 必填 | 默认值 | 说明         | API_LEVEL |
| ------- | ------------------- | ---- | ------ | ------------ | --------- |
| oldPath | <code>string</code> | 是   | -      | 旧的文件路径 | 2.0       |
| newPath | <code>string</code> | 是   | -      | 新的文件路径 | 2.0       |

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { renameSync } from '@zos/fs'

const result = renameSync({
  oldPath: 'test.txt',
  newPath: 'new_test.txt',
})

if (result === 0) {
  console.log('renameSync success')
}
```
