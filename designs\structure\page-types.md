---
sidebar_label: 页面类型
---

# 页面类型

| **页面类型**                                                 | **优先级** | **典型页面举例**                                             |
| ------------------------------------------------------------ | ---------- | ------------------------------------------------------------ |
| 常驻                                                         | P0         | 表盘同步                                                     |
| 浮层 - 重要提醒<br/>浮层 - 普通弹窗<br/>浮层 - 消息提醒<br/>浮层 - 短提醒 | P1         | 浮层 - 重要提醒：闹钟提醒<br/>浮层 - 普通弹窗：续航预警<br/>浮层 - 消息提醒：达标提醒、通知<br/>浮层 - 短提醒：微信、支付宝开通成功、表盘同步成功 |
| 基础<br/>表盘<br/>基础 - 进行中状态                                  | P2         | 基础：天气<br/>表盘：表盘、副屏<br/>基础 - 进行中状态：计时中      |

## 使用规范

- 同优先层级页面按时间顺序先后，后出现的页面覆盖先出现的页面，优先层级低的页面不会覆盖在更高层级页面的上面。
- 不支持右滑退出的页面直接覆盖出现，出场动画见[覆盖页面](switching-pages.md)。
