---
title: hmFS.SysProSetBool(key, val)
sidebar_label: SysProSetBool
---

存储临时布尔值，系统重启将清除

## 类型

```ts
(key: string, val: boolean) => result
```

## 参数

| 参数 | 说明     | 必填 | 类型     | 默认值 |
| ---- | -------- | ---- | -------- | ------ |
| key  | 键字符串 | 是   | `string` | -      |
| val  | 存储的布尔值 | 是   | `boolean` | -      |

### result

| 说明                   | 类型     |
| ---------------------- | -------- |
| 操作结果，`0` 表示成功 | `number` |

## 用法

```js
hmFS.SysProSetBool('test_key', true)
```
