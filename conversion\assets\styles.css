/**
 * 进制转换器样式文件
 * 为 Zepp OS 应用提供样式支持
 */

/* 键盘容器样式 */
.keyboard-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 320px;
  background: rgba(26, 26, 26, 0.95);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(20px);
  transform: translateY(calc(100% - 60px));
  transition: transform 0.3s ease;
  z-index: 50;
}

.keyboard-container.expanded {
  transform: translateY(0);
}

/* 键盘拉手样式 */
.keyboard-handle {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.keyboard-handle-bar {
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  transition: background 0.2s;
}

.keyboard-handle:hover .keyboard-handle-bar {
  background: rgba(255, 255, 255, 0.6);
}

.keyboard-hint {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(76, 175, 80, 0.2);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  color: #4CAF50;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* 键盘内容样式 */
.keyboard-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 0 10px;
}

.keyboard-row {
  display: flex;
  gap: 6px;
  justify-content: center;
}

/* 按键样式 */
.key {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  min-height: 40px;
  flex: 1;
  max-width: 38px;
}

.key:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.key:active, .key.pressed {
  transform: scale(0.95);
}

.key.special {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  font-size: 14px;
}

.key.special:hover {
  background: rgba(76, 175, 80, 0.5);
}

.key.delete {
  background: rgba(244, 67, 54, 0.3);
  border-color: #f44336;
  font-size: 18px;
}

.key.delete:hover {
  background: rgba(244, 67, 54, 0.5);
}

.key.caps {
  background: rgba(255, 193, 7, 0.3);
  border-color: #FFC107;
  font-size: 14px;
}

.key.caps:hover {
  background: rgba(255, 193, 7, 0.5);
}

.key.caps.active {
  background: rgba(255, 193, 7, 0.6);
  color: #000;
}

.key.wide {
  flex: 1.5;
  max-width: 60px;
}

.key.extra-wide {
  flex: 2.5;
  max-width: 200px;
}

/* 数字行样式 */
.numbers-row {
  justify-content: space-between;
}

.numbers-row .key {
  max-width: 36px;
}

/* 功能键行样式 */
.function-row {
  justify-content: center;
  margin-top: 8px;
  gap: 12px;
}

.function-row .key {
  flex: 0 0 auto;
}

/* 结果显示区样式 */
.results-section {
  position: absolute;
  top: 180px;
  left: 20px;
  right: 20px;
  bottom: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 12px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  transition: bottom 0.3s ease;
}

.results-section.keyboard-expanded {
  bottom: 340px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  color: #cccccc;
  font-size: 16px;
  font-weight: 500;
}

.result-value {
  color: #ffffff;
  font-size: 18px;
  font-family: 'Courier New', monospace;
  text-align: right;
  word-break: break-all;
}

/* 输入显示区样式 */
.input-display {
  font-size: 32px;
  color: #4CAF50;
  text-align: center;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  padding: 8px;
}

.current-base {
  font-size: 16px;
  color: #cccccc;
  text-align: left;
  padding: 4px 8px;
}

.mode-indicator {
  font-size: 14px;
  color: #2196F3;
  text-align: right;
  padding: 4px 8px;
}

/* 标题样式 */
.title-bar {
  font-size: 24px;
  color: #ffffff;
  text-align: center;
  font-weight: bold;
  padding: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .keyboard-content {
    max-width: 100%;
    padding: 0 5px;
  }
  
  .key {
    font-size: 14px;
    min-height: 36px;
  }
  
  .numbers-row .key {
    max-width: 32px;
  }
}
