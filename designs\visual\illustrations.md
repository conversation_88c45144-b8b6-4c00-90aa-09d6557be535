---
sidebar_label: 插图
---

#  插图  

插图作为产品视觉体验中的重要组成部分，通过图形化手段表达信息，可以在一定程度上帮助产品提升易读性、美观性，并刻画产品品牌性。
 
## 设计原则  

Zepp OS 的插图遵循「轻量」、「友好」、「有效」的设计原则，需要做到图形轻量简洁，减少过多造型组合，设计表现友好且赋予适度的情感 ，表意清晰易于理解，保证用户的操作效率。

## 插图类型  

根据使用场景不同，插图分为以下类型：状态说明、引导反馈、勋章。  

根据插图元素不同，页面信息表达主次不同将插图分为：大尺寸、中等尺寸、小尺寸。
## 状态说明  

阐明页面状态的插图类型，具有一定说明性、安抚性与装饰性。  

**重要提示**  

用于表示系统或应用的重要提示，彩色插图或彩色灰色搭配的插图。
![Design](/img/design/important-notice_1.png)
>①使用彩色与灰色搭配的插图来表示特定重要事件的提示。
>
>②使用彩色插图来表示重要的状态信息

![Design](/img/design/important-notice_2.png)

![Design](/img/design/important-notice_3.png)
>③系统，电量过低提示（中等尺寸）。
>
>④运动，监测开启后电池寿命会降低提示（中等尺寸）。

**功能提示**  

用于表示应用内功能提示，彩色插图。  

需要突出页面内容或操作时，可以使用小插图。  

![Design](/img/design/function-prompt.png)

![Design](/img/design/function-prompt_2.png)

![Design](/img/design/function-prompt_3.png)  

>①太阳与月亮，日出提示（中等尺寸）。
>
>②闹钟，起床提示（小尺寸）。

**使用提示** 

**用于异常状态时进行使用提示，灰色插图。**  

![Design](/img/design/instructions-for-use_1.png)

![Design](/img/design/instructions-for-use_2.png)

![Design](/img/design/instructions-for-use_3.png)  

>①世界时钟，夏令时过期提示（中等尺寸）。
>
>②运动，同步运动数据提示（中等尺寸）。

## 引导反馈  

用于引导用户进行操作，或是对用户操作进行相应反馈的插图类型，具有一定的说明性、引导性与装饰性。  

![Design](/img/design/guided-feedback_1.png)  

当小尺寸插图无法清晰展示引导内容时，使用大尺寸插图。  

![Design](/img/design/guided-feedback_2.png)  

为保证插图的表意明确，引导反馈类插图通常会使用动态表现。  

![Design](/img/design/guided-feedback_3.png)

## 勋章  

勋章向用户传达带有时间信息的应用通知内容或者身体状况提醒，具有一定的激励性、装饰性与说明性。  

![Design](/img/design/medal.png)

## 使用规则  

- 系统插图的基础视觉样式为白色图形样式①。
- 各应用空页面状态插图请使用灰色图形样式②；
- 副屏空页面（表示需连接手机应用进行操作），请使用彩色图形样式，为保证用户理解，通常使用该应用图标③  ；
- 包含动画的引导反馈插图，可在背景层添加表装饰的全屏幕动画。  

![Design](/img/design/usage-rules_1.png)
>①系统插图的基础视觉样式
>
>②应用空页面插图
>
>③副屏空页面插图（表示需连接手机应用进行操作状态的空页面）

- 方屏设备需要考虑状态栏存在，应用内页面插图无需标题。  

![Design](/img/design/usage-rules_2.png)
>④方屏设备系统插图的基础视觉样式
>
>⑤方屏设备应用空页面插图

## 视觉规范  

插图的主体切图大小为③ ，内部需预留空白透明的安全区域。  

插画图形区域为①，其中绘制区域一般应保持在②的范围内。如图形需要添加额外视觉重量，保证与其他图标的一致，则绘制区域可以延伸至保留区域内（注意：切图时边缘需要保留2px安全距离）。  

![Design](/img/design/visual-guidelines.png)  
>① 图标主体的基础绘制区域
>
>② 图标主体的保留区域
>
>③ 图标主体的最终实际尺寸