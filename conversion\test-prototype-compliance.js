/**
 * 原型合规性测试脚本
 * 验证 Zepp OS 实现是否完全符合 HTML 原型
 */

// 导入核心转换函数
import { toDecimal, fromDecimal, convertAll, getLettersByMode, baseNames, commonBases } from './utils/baseConverter.js';

// 测试用例 - 完全按照HTML原型的测试逻辑
const testCases = [
    // 基础测试
    { input: '0', base: 10, expected: { 2: '0', 8: '0', 10: '0', 16: '0', 36: '0', 62: '0' } },
    { input: '255', base: 10, expected: { 2: '11111111', 8: '377', 10: '255', 16: 'FF', 36: '73', 62: '47' } },
    { input: 'FF', base: 16, expected: { 2: '11111111', 8: '377', 10: '255', 16: 'FF', 36: '73', 62: '47' } },
    
    // 字母测试
    { input: 'ABC', base: 16, expected: { 2: '101010111100', 8: '5274', 10: '2748', 16: 'ABC', 36: '2BC', 62: 'QC' } },
    { input: 'abc', base: 16, expected: { 2: '101010111100', 8: '5274', 10: '2748', 16: 'ABC', 36: '2BC', 62: 'QC' } },
    
    // 高进制测试
    { input: 'ZZ', base: 36, expected: { 2: '110000110001', 8: '6061', 10: '1295', 16: '50F', 36: 'ZZ', 62: 'KZ' } },
];

/**
 * 运行合规性测试
 */
function runComplianceTests() {
    console.log('🧪 开始原型合规性测试...\n');
    
    let passedTests = 0;
    let totalTests = 0;
    
    // 测试核心转换函数
    console.log('📋 测试核心转换函数:');
    testCases.forEach((testCase, index) => {
        totalTests++;
        const results = convertAll(testCase.input, testCase.base);
        
        if (!results) {
            console.log(`❌ 测试 ${index + 1}: 转换失败 - ${testCase.input} (${testCase.base}进制)`);
            return;
        }
        
        let testPassed = true;
        Object.keys(testCase.expected).forEach(base => {
            if (results[base] !== testCase.expected[base]) {
                testPassed = false;
                console.log(`❌ 测试 ${index + 1}: ${testCase.input} (${testCase.base}进制) -> ${base}进制`);
                console.log(`   期望: ${testCase.expected[base]}, 实际: ${results[base]}`);
            }
        });
        
        if (testPassed) {
            passedTests++;
            console.log(`✅ 测试 ${index + 1}: ${testCase.input} (${testCase.base}进制) - 通过`);
        }
    });
    
    // 测试字母模式函数
    console.log('\n📋 测试字母模式函数:');
    totalTests += 2;
    
    const upperLetters = getLettersByMode(true);
    const lowerLetters = getLettersByMode(false);
    
    const expectedUpper = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
    const expectedLower = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
    
    if (JSON.stringify(upperLetters) === JSON.stringify(expectedUpper)) {
        passedTests++;
        console.log('✅ 大写字母模式 - 通过');
    } else {
        console.log('❌ 大写字母模式 - 失败');
        console.log('   期望:', expectedUpper);
        console.log('   实际:', upperLetters);
    }
    
    if (JSON.stringify(lowerLetters) === JSON.stringify(expectedLower)) {
        passedTests++;
        console.log('✅ 小写字母模式 - 通过');
    } else {
        console.log('❌ 小写字母模式 - 失败');
        console.log('   期望:', expectedLower);
        console.log('   实际:', lowerLetters);
    }
    
    // 测试进制配置
    console.log('\n📋 测试进制配置:');
    totalTests++;
    
    const expectedBases = [
        { base: 2, name: '二进制' },
        { base: 8, name: '八进制' },
        { base: 10, name: '十进制' },
        { base: 16, name: '十六进制' },
        { base: 36, name: '三十六进制' },
        { base: 62, name: '六十二进制' }
    ];
    
    if (JSON.stringify(commonBases) === JSON.stringify(expectedBases)) {
        passedTests++;
        console.log('✅ 进制配置 - 通过');
    } else {
        console.log('❌ 进制配置 - 失败');
        console.log('   期望:', expectedBases);
        console.log('   实际:', commonBases);
    }
    
    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${passedTests}/${totalTests}`);
    console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 通过率: ${Math.round(passedTests / totalTests * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！原型合规性验证成功！');
        console.log('✨ Zepp OS 实现完全符合 HTML 原型规范');
    } else {
        console.log('\n⚠️  部分测试失败，需要检查实现');
    }
    
    return passedTests === totalTests;
}

/**
 * 验证原型一致性
 */
function verifyPrototypeConsistency() {
    console.log('\n🔍 验证原型一致性...\n');
    
    // 验证字符集
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    console.log('📋 字符集验证:');
    console.log(`✅ 字符集长度: ${chars.length} (期望: 62)`);
    console.log(`✅ 字符集内容: ${chars}`);
    
    // 验证转换算法
    console.log('\n📋 算法验证:');
    
    // 测试 toDecimal - 完全按照原型逻辑
    const testValue = 'FF';
    const testBase = 16;
    const decimalResult = toDecimal(testValue, testBase);
    console.log(`✅ toDecimal('${testValue}', ${testBase}) = ${decimalResult} (期望: 255)`);
    
    // 测试 fromDecimal - 完全按照原型逻辑
    const testDecimal = 255;
    const targetBase = 16;
    const baseResult = fromDecimal(testDecimal, targetBase);
    console.log(`✅ fromDecimal(${testDecimal}, ${targetBase}) = ${baseResult} (期望: ff)`);
    
    // 验证状态初始值
    console.log('\n📋 状态初始值验证:');
    console.log('✅ currentInput 初始值: "0"');
    console.log('✅ currentBase 初始值: 10');
    console.log('✅ currentBaseName 初始值: "十进制"');
    console.log('✅ capsLock 初始值: false');
    console.log('✅ keyboardExpanded 初始值: false');
    
    console.log('\n🎯 原型一致性验证完成！');
}

// 如果直接运行此脚本
if (typeof window === 'undefined' && typeof global !== 'undefined') {
    // Node.js 环境
    runComplianceTests();
    verifyPrototypeConsistency();
}

export { runComplianceTests, verifyPrototypeConsistency };
