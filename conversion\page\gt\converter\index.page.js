/**
 * 进制转换器主页面
 * 支持 2-62 进制转换，26键专业布局
 */

import { createWidget, widget, align, text_style, event, prop } from '@zos/ui'
import { px } from '@zos/utils'
import { push } from '@zos/router'
import { LocalStorage } from '@zos/storage'
import { Vibrator } from '@zos/sensor'

// 导入工具类
import { baseConverter } from '../../../utils/baseConverter'

// 根据设备类型导入对应布局
import * as Layout from './index.page.r.layout'

// 应用状态
let appState = {
  currentInput: '0',
  currentBase: 10,
  currentBaseName: '十进制',
  capsLock: false,
  keyboardExpanded: false,
  results: {}
};

// UI 组件引用
let uiComponents = {
  inputDisplay: null,
  currentBaseDisplay: null,
  modeIndicator: null,
  resultsBackground: null,
  resultItems: [],
  keyboardContainer: null
};

// 常用进制配置
const COMMON_BASES = [
  { base: 2, name: '二进制', shortName: 'BIN' },
  { base: 8, name: '八进制', shortName: 'OCT' },
  { base: 10, name: '十进制', shortName: 'DEC' },
  { base: 16, name: '十六进制', shortName: 'HEX' },
  { base: 32, name: '三十二进制', shortName: 'B32' },
  { base: 36, name: '三十六进制', shortName: 'B36' },
  { base: 62, name: '六十二进制', shortName: 'B62' }
];

// 振动器实例
let vibrator = null;

Page({
  onInit() {
    console.log('进制转换器初始化');
    this.initVibrator();
    this.loadState();
    this.createUI();
    this.createKeyboard();
    this.updateAllResults();
  },

  onDestroy() {
    console.log('进制转换器销毁');
    this.saveState();
  },

  // 初始化振动器
  initVibrator() {
    try {
      vibrator = new Vibrator();
    } catch (error) {
      console.log('振动器初始化失败:', error);
    }
  },

  // 加载保存的状态
  loadState() {
    try {
      const storage = new LocalStorage();
      const savedState = storage.getItem('converter_state');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        appState = { ...appState, ...parsed };
      }
    } catch (error) {
      console.log('状态加载失败:', error);
    }
  },

  // 保存状态
  saveState() {
    try {
      const storage = new LocalStorage();
      storage.setItem('converter_state', JSON.stringify(appState));
    } catch (error) {
      console.log('状态保存失败:', error);
    }
  },

  // 创建UI界面
  createUI() {
    // 创建背景
    Layout.createBackground();

    // 创建标题栏
    Layout.createTitleBar();

    // 创建输入显示
    uiComponents.inputDisplay = Layout.createInputDisplay();

    // 创建当前进制显示
    uiComponents.currentBaseDisplay = Layout.createCurrentBase();

    // 创建模式指示器
    uiComponents.modeIndicator = Layout.createModeIndicator();

    // 创建结果显示区
    this.createResultsSection();

    // 更新显示
    this.updateInputDisplay();
    this.updateCurrentBaseDisplay();
    this.updateModeIndicator();
  },

  // 创建结果显示区
  createResultsSection() {
    // 创建背景
    uiComponents.resultsBackground = Layout.createResultsBackground();

    // 创建结果项
    uiComponents.resultItems = [];
    COMMON_BASES.forEach((baseConfig, index) => {
      const resultItem = Layout.createResultItem(
        index,
        `${baseConfig.name}:`,
        '0'
      );
      uiComponents.resultItems.push({
        base: baseConfig.base,
        labelWidget: resultItem.labelWidget,
        valueWidget: resultItem.valueWidget
      });
    });
  },

  // 创建键盘
  createKeyboard() {
    // 创建键盘容器背景
    uiComponents.keyboardContainer = createWidget(widget.FILL_RECT, {
      ...Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER,
      ...Layout.KEYBOARD_STYLES.CONTAINER
    });

    // 创建键盘拉手
    this.createKeyboardHandle();

    // 创建键盘按键
    this.createKeyboardKeys();
  },

  // 创建键盘拉手
  createKeyboardHandle() {
    const handleY = Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y + px(28);

    // 拉手背景
    createWidget(widget.FILL_RECT, {
      x: px(220),
      y: handleY,
      ...Layout.KEYBOARD_STYLES.HANDLE
    });

    // 拉手点击区域
    const handleClickArea = createWidget(widget.BUTTON, {
      x: px(190),
      y: Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y,
      w: px(100),
      h: px(60),
      color: 0x000000,
      text: '',
      click_func: () => {
        this.toggleKeyboard();
      }
    });
  },

  // 创建键盘按键
  createKeyboardKeys() {
    const keyboardY = Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y + px(60);
    const keyWidth = px(36);
    const keyHeight = px(40);
    const keyGap = px(6);

    // 数字行
    this.createNumberRow(keyboardY, keyWidth, keyHeight, keyGap);

    // 字母行
    this.createLetterRows(keyboardY + px(48), keyWidth, keyHeight, keyGap);

    // 功能键行
    this.createFunctionRow(keyboardY + px(192), keyWidth, keyHeight, keyGap);
  },

  // 创建数字行
  createNumberRow(startY, keyWidth, keyHeight, keyGap) {
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    const startX = px(20);

    numbers.forEach((num, index) => {
      const keyX = startX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY, keyWidth, keyHeight, num, 'input');
    });

    // 删除键
    const deleteX = startX + 10 * (keyWidth + keyGap);
    this.createKey(deleteX, startY, keyWidth, keyHeight, '⌫', 'delete', Layout.KEYBOARD_STYLES.KEY_DELETE);
  },

  // 创建字母行
  createLetterRows(startY, keyWidth, keyHeight, keyGap) {
    const letters = this.getLettersByMode();

    // 第一行: QWERTYUIOP
    const row1 = letters.slice(0, 10);
    const row1StartX = px(40);
    row1.forEach((letter, index) => {
      const keyX = row1StartX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY, keyWidth, keyHeight, letter, 'input');
    });

    // 第二行: ASDFGHJKL
    const row2 = letters.slice(10, 19);
    const row2StartX = px(58);
    row2.forEach((letter, index) => {
      const keyX = row2StartX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY + px(48), keyWidth, keyHeight, letter, 'input');
    });

    // 第三行: ↑ZXCVBNM
    const row3StartX = px(40);
    // 大小写切换键
    this.createKey(row3StartX, startY + px(96), keyWidth, keyHeight, '↑', 'caps',
      appState.capsLock ? { ...Layout.KEYBOARD_STYLES.KEY_CAPS, color: 0xFFC107 } : Layout.KEYBOARD_STYLES.KEY_CAPS);

    // 第三行字母
    const row3 = letters.slice(19, 26);
    row3.forEach((letter, index) => {
      const keyX = row3StartX + (index + 1) * (keyWidth + keyGap);
      this.createKey(keyX, startY + px(96), keyWidth, keyHeight, letter, 'input');
    });
  },

  // 创建功能键行
  createFunctionRow(startY, keyWidth, keyHeight, keyGap) {
    // 进制选择键
    this.createKey(px(80), startY, px(200), keyHeight, '进制选择', 'base-select', Layout.KEYBOARD_STYLES.KEY_SPECIAL);

    // 清空键
    this.createKey(px(300), startY, px(100), keyHeight, '清空', 'clear', Layout.KEYBOARD_STYLES.KEY_DELETE);
  },

  // 创建单个按键
  createKey(x, y, width, height, text, action, customStyle = {}) {
    const keyStyle = { ...Layout.KEYBOARD_STYLES.KEY, ...customStyle };

    const keyButton = createWidget(widget.BUTTON, {
      x: x,
      y: y,
      w: width,
      h: height,
      radius: keyStyle.radius,
      normal_color: keyStyle.color,
      press_color: this.adjustColor(keyStyle.color, 0.8),
      text: text,
      text_size: keyStyle.text_size || px(16),
      color: keyStyle.text_color || 0xffffff,
      click_func: () => {
        this.handleKeyPress(action, text);
      }
    });

    return keyButton;
  },

  // 调整颜色亮度
  adjustColor(color, factor) {
    const r = Math.floor(((color >> 16) & 0xFF) * factor);
    const g = Math.floor(((color >> 8) & 0xFF) * factor);
    const b = Math.floor((color & 0xFF) * factor);
    return (r << 16) | (g << 8) | b;
  },

  // 获取当前模式的字母
  getLettersByMode() {
    const upperLetters = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
    const lowerLetters = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
    return appState.capsLock ? upperLetters : lowerLetters;
  },

  // 处理按键事件
  handleKeyPress(action, text) {
    // 触觉反馈
    this.vibrate();

    switch (action) {
      case 'input':
        this.inputCharacter(text);
        break;
      case 'delete':
        this.deleteCharacter();
        break;
      case 'clear':
        this.clearInput();
        break;
      case 'caps':
        this.toggleCapsLock();
        break;
      case 'base-select':
        this.showBaseSelector();
        break;
    }
  },

  // 输入字符
  inputCharacter(char) {
    // 验证字符是否在当前进制下有效
    if (!baseConverter.isValidInput(char, appState.currentBase)) {
      console.log(`字符 ${char} 在 ${appState.currentBase} 进制下无效`);
      return;
    }

    // 如果当前输入是 '0'，替换而不是追加
    if (appState.currentInput === '0') {
      appState.currentInput = char;
    } else {
      appState.currentInput += char;
    }

    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 删除字符
  deleteCharacter() {
    if (appState.currentInput.length > 1) {
      appState.currentInput = appState.currentInput.slice(0, -1);
    } else {
      appState.currentInput = '0';
    }

    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 清空输入
  clearInput() {
    appState.currentInput = '0';
    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 切换大小写
  toggleCapsLock() {
    appState.capsLock = !appState.capsLock;
    this.updateModeIndicator();
    // 重新创建键盘以更新字母显示
    this.recreateKeyboard();
  },

  // 显示进制选择器
  showBaseSelector() {
    // 这里可以实现进制选择弹窗
    // 暂时使用简单的循环切换常用进制
    const currentIndex = COMMON_BASES.findIndex(base => base.base === appState.currentBase);
    const nextIndex = (currentIndex + 1) % COMMON_BASES.length;
    const nextBase = COMMON_BASES[nextIndex];

    this.changeBase(nextBase.base, nextBase.name);
  },

  // 切换进制
  changeBase(newBase, newBaseName) {
    appState.currentBase = newBase;
    appState.currentBaseName = newBaseName;

    this.updateCurrentBaseDisplay();
    this.updateAllResults();
    this.recreateKeyboard(); // 重新创建键盘以更新有效按键
  },

  // 切换键盘展开状态
  toggleKeyboard() {
    appState.keyboardExpanded = !appState.keyboardExpanded;

    if (appState.keyboardExpanded) {
      this.expandKeyboard();
    } else {
      this.collapseKeyboard();
    }
  },

  // 展开键盘
  expandKeyboard() {
    // 移动键盘容器位置
    uiComponents.keyboardContainer.setProperty(prop.Y, px(160));

    // 调整结果显示区域
    uiComponents.resultsBackground.setProperty(prop.H, px(140));
  },

  // 收起键盘
  collapseKeyboard() {
    // 移动键盘容器位置
    uiComponents.keyboardContainer.setProperty(prop.Y, Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y);

    // 恢复结果显示区域
    uiComponents.resultsBackground.setProperty(prop.H, Layout.LAYOUT_CONFIG.RESULTS_SECTION.h);
  },

  // 更新输入显示
  updateInputDisplay() {
    const displayText = baseConverter.formatDisplay(appState.currentInput, appState.currentBase);
    uiComponents.inputDisplay.setProperty(prop.TEXT, displayText);
  },

  // 更新当前进制显示
  updateCurrentBaseDisplay() {
    const text = `当前: ${appState.currentBaseName} (${appState.currentBase})`;
    uiComponents.currentBaseDisplay.setProperty(prop.TEXT, text);
  },

  // 更新模式指示器
  updateModeIndicator() {
    const text = appState.capsLock ? '大写' : '小写';
    uiComponents.modeIndicator.setProperty(prop.TEXT, text);
  },

  // 更新所有转换结果
  updateAllResults() {
    const results = baseConverter.convertToMultiple(
      appState.currentInput,
      appState.currentBase,
      COMMON_BASES.map(base => base.base)
    );

    if (results) {
      appState.results = results;
      this.updateResultsDisplay();
    }
  },

  // 更新结果显示
  updateResultsDisplay() {
    uiComponents.resultItems.forEach(item => {
      const result = appState.results[item.base];
      if (result !== undefined) {
        const formattedResult = baseConverter.formatDisplay(result, item.base);
        item.valueWidget.setProperty(prop.TEXT, formattedResult);
      }
    });
  },

  // 重新创建键盘
  recreateKeyboard() {
    // 这里应该重新创建键盘，但由于 Zepp OS 的限制，
    // 我们暂时只更新显示相关的内容
    this.updateModeIndicator();
  },

  // 触觉反馈
  vibrate() {
    if (vibrator) {
      try {
        vibrator.start();
        setTimeout(() => {
          vibrator.stop();
        }, 50);
      } catch (error) {
        console.log('振动失败:', error);
      }
    }
  }

});
