/**
 * 进制转换器主页面 - 严格按照HTML原型实现
 * 一比一复刻原型的所有功能和界面
 */

import { createWidget, widget, align, text_style, event, prop } from '@zos/ui'
import { px } from '@zos/utils'
import { LocalStorage } from '@zos/storage'
import { Vibrator } from '@zos/sensor'

// 导入工具类 - 按照原型逻辑
import { toDecimal, fromDecimal, convertAll, getLettersByMode, baseNames, commonBases } from '../../../utils/baseConverter'

// 应用状态 - 完全按照HTML原型
let currentInput = '0';
let currentBase = 10;
let currentBaseName = '十进制';
let capsLock = false;
let keyboardExpanded = false;

// UI 组件引用
let uiComponents = {
  inputDisplay: null,
  currentBaseDisplay: null,
  modeIndicator: null,
  resultsSection: null,
  resultItems: {},
  keyboardContainer: null,
  keyboardContent: null,
  keyboardHint: null,
  baseSelector: null
};

// 振动器实例
let vibrator = null;

Page({
  onInit() {
    console.log('进制转换器初始化');
    this.initVibrator();
    this.createUI();
    this.updateKeyboardDisplay();
    this.convertAllBases();
  },

  onDestroy() {
    console.log('进制转换器销毁');
  },

  // 初始化振动器
  initVibrator() {
    try {
      vibrator = new Vibrator();
    } catch (error) {
      console.log('振动器初始化失败:', error);
    }
  },

  // 创建UI界面 - 严格按照HTML原型结构
  createUI() {
    // 创建背景
    this.createBackground();

    // 创建标题栏
    this.createTitleBar();

    // 创建输入显示区
    this.createInputSection();

    // 创建结果显示区
    this.createResultsSection();

    // 创建键盘容器
    this.createKeyboardContainer();

    // 创建进制选择器
    this.createBaseSelector();

    // 更新显示
    this.updateDisplay();
  },

  // 创建背景
  createBackground() {
    createWidget(widget.FILL_RECT, {
      x: 0,
      y: 0,
      w: px(480),
      h: px(480),
      color: 0x000000
    });
  },

  // 创建标题栏
  createTitleBar() {
    createWidget(widget.TEXT, {
      x: px(20),
      y: px(20),
      w: px(440),
      h: px(48),
      text: '进制转换器',
      text_size: px(24),
      color: 0xffffff,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    });
  },

  // 创建输入显示区
  createInputSection() {
    // 输入值显示
    uiComponents.inputDisplay = createWidget(widget.TEXT, {
      x: px(20),
      y: px(80),
      w: px(440),
      h: px(48),
      text: '0',
      text_size: px(32),
      color: 0x4CAF50,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    });

    // 当前进制显示
    uiComponents.currentBaseDisplay = createWidget(widget.TEXT, {
      x: px(20),
      y: px(128),
      w: px(300),
      h: px(36),
      text: '当前: 十进制 (10)',
      text_size: px(16),
      color: 0xcccccc,
      align_h: align.LEFT,
      align_v: align.CENTER_V
    });

    // 模式指示器
    uiComponents.modeIndicator = createWidget(widget.TEXT, {
      x: px(360),
      y: px(128),
      w: px(100),
      h: px(36),
      text: '小写',
      text_size: px(14),
      color: 0x2196F3,
      align_h: align.RIGHT,
      align_v: align.CENTER_V
    });
  },

  // 创建结果显示区 - 按照HTML原型的结构
  createResultsSection() {
    // 结果显示背景
    uiComponents.resultsSection = createWidget(widget.FILL_RECT, {
      x: px(20),
      y: px(180),
      w: px(440),
      h: px(220),
      radius: px(16),
      color: 0x0d1117
    });

    // 创建各进制结果显示 - 按照原型的bases顺序 [2, 8, 10, 16, 36, 62]
    const bases = [2, 8, 10, 16, 36, 62];
    const baseLabels = ['二进制:', '八进制:', '十进制:', '十六进制:', '三十六进制:', '六十二进制:'];

    bases.forEach((base, index) => {
      const itemY = px(180 + 12 + index * 32);

      // 标签
      createWidget(widget.TEXT, {
        x: px(36),
        y: itemY,
        w: px(120),
        h: px(28),
        text: baseLabels[index],
        text_size: px(16),
        color: 0xcccccc,
        align_h: align.LEFT,
        align_v: align.CENTER_V
      });

      // 结果值
      uiComponents.resultItems[base] = createWidget(widget.TEXT, {
        x: px(160),
        y: itemY,
        w: px(284),
        h: px(28),
        text: '0',
        text_size: px(18),
        color: 0xffffff,
        align_h: align.RIGHT,
        align_v: align.CENTER_V
      });
    });
  },

  // 创建键盘容器 - 严格按照HTML原型
  createKeyboardContainer() {
    // 键盘容器背景
    uiComponents.keyboardContainer = createWidget(widget.FILL_RECT, {
      x: px(0),
      y: px(400), // 默认收起位置
      w: px(480),
      h: px(320),
      radius: px(20),
      color: 0x1a1a1a
    });

    // 键盘拉手
    createWidget(widget.FILL_RECT, {
      x: px(220),
      y: px(428),
      w: px(40),
      h: px(4),
      radius: px(2),
      color: 0x666666
    });

    // 键盘提示文字
    uiComponents.keyboardHint = createWidget(widget.TEXT, {
      x: px(190),
      y: px(340),
      w: px(100),
      h: px(20),
      text: '上拉展开键盘',
      text_size: px(12),
      color: 0x4CAF50,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    });

    // 键盘拉手点击区域
    createWidget(widget.BUTTON, {
      x: px(190),
      y: px(400),
      w: px(100),
      h: px(60),
      text: '',
      normal_color: 0x000000,
      press_color: 0x333333,
      click_func: () => {
        this.toggleKeyboardExpansion();
      }
    });

    // 键盘内容区域（动态生成）
    this.createKeyboardKeys();
  },

  // 创建键盘按键 - 严格按照HTML原型的键盘布局
  createKeyboardKeys() {
    const keyboardY = px(460);
    const keyWidth = px(36);
    const keyHeight = px(40);
    const keyGap = px(6);

    // 数字行 - 完全按照原型布局
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    const numbersStartX = px(20);

    numbers.forEach((num, index) => {
      const keyX = numbersStartX + index * (keyWidth + keyGap);
      this.createKey(keyX, keyboardY, keyWidth, keyHeight, num, () => {
        this.inputDigit(num);
      });
    });

    // 删除键
    this.createKey(numbersStartX + 10 * (keyWidth + keyGap), keyboardY, keyWidth, keyHeight, '⌫', () => {
      this.deleteDigit();
    }, 0xf44336);

    // 字母行将在 updateKeyboardDisplay 中动态创建
    this.createLetterKeys();

    // 功能键行
    this.createFunctionKeys();
  },

  // 创建字母按键 - 简化版本，直接创建固定按键
  createLetterKeys() {
    const keyWidth = px(36);
    const keyHeight = px(40);
    const keyGap = px(6);

    // 第一行字母 QWERTYUIOP - 创建固定按键，点击时根据当前大小写状态输入
    const row1Y = px(508);
    const row1StartX = px(40);
    const row1Letters = ['Q','W','E','R','T','Y','U','I','O','P'];

    row1Letters.forEach((letter, i) => {
      const keyX = row1StartX + i * (keyWidth + keyGap);
      this.createKey(keyX, row1Y, keyWidth, keyHeight, letter, () => {
        const actualLetter = capsLock ? letter : letter.toLowerCase();
        this.inputDigit(actualLetter);
      });
    });

    // 第二行字母 ASDFGHJKL
    const row2Y = px(556);
    const row2StartX = px(58);
    const row2Letters = ['A','S','D','F','G','H','J','K','L'];

    row2Letters.forEach((letter, i) => {
      const keyX = row2StartX + i * (keyWidth + keyGap);
      this.createKey(keyX, row2Y, keyWidth, keyHeight, letter, () => {
        const actualLetter = capsLock ? letter : letter.toLowerCase();
        this.inputDigit(actualLetter);
      });
    });

    // 第三行 ↑ZXCVBNM
    const row3Y = px(604);
    const row3StartX = px(40);

    // 大小写切换键
    this.createKey(row3StartX, row3Y, keyWidth, keyHeight, '↑', () => {
      this.toggleCapsLock();
    }, 0xFFC107);

    // 第三行字母
    const row3Letters = ['Z','X','C','V','B','N','M'];
    row3Letters.forEach((letter, i) => {
      const keyX = row3StartX + (i + 1) * (keyWidth + keyGap);
      this.createKey(keyX, row3Y, keyWidth, keyHeight, letter, () => {
        const actualLetter = capsLock ? letter : letter.toLowerCase();
        this.inputDigit(actualLetter);
      });
    });
  },

  // 创建功能键
  createFunctionKeys() {
    const funcY = px(652);

    // 进制选择键
    this.createKey(px(80), funcY, px(200), px(40), '进制选择', () => {
      this.showBaseSelector();
    }, 0x4CAF50);

    // 清空键
    this.createKey(px(300), funcY, px(100), px(40), '清空', () => {
      this.clearInput();
    }, 0xf44336);
  },

  // 创建单个按键
  createKey(x, y, width, height, text, clickFunc, color = 0x2d2d2d) {
    return createWidget(widget.BUTTON, {
      x: x,
      y: y,
      w: width,
      h: height,
      text: text,
      text_size: px(16),
      color: 0xffffff,
      normal_color: color,
      press_color: this.adjustColor(color, 0.8),
      radius: px(8),
      click_func: () => {
        this.vibrate();
        clickFunc();
      }
    });
  },

  // 调整颜色亮度
  adjustColor(color, factor) {
    const r = Math.floor(((color >> 16) & 0xFF) * factor);
    const g = Math.floor(((color >> 8) & 0xFF) * factor);
    const b = Math.floor((color & 0xFF) * factor);
    return (r << 16) | (g << 8) | b;
  },

  // 创建进制选择器 - 按照HTML原型
  createBaseSelector() {
    // 选择器背景（默认隐藏）
    uiComponents.baseSelector = createWidget(widget.FILL_RECT, {
      x: px(40),
      y: px(120),
      w: px(400),
      h: px(280),
      radius: px(16),
      color: 0x1a1a1a
    });

    // 隐藏选择器
    uiComponents.baseSelector.setProperty(prop.VISIBLE, false);

    // 选择器标题
    createWidget(widget.TEXT, {
      x: px(40),
      y: px(130),
      w: px(400),
      h: px(40),
      text: '选择输入进制',
      text_size: px(18),
      color: 0xffffff,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    });

    // 创建进制选项 - 按照原型的commonBases
    commonBases.forEach((baseConfig, index) => {
      const optionY = px(180 + index * 40);
      const isSelected = baseConfig.base === currentBase;

      createWidget(widget.BUTTON, {
        x: px(60),
        y: optionY,
        w: px(360),
        h: px(36),
        text: `${isSelected ? '●' : '○'} ${baseConfig.name} (${baseConfig.base})`,
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x2d2d2d,
        press_color: 0x4CAF50,
        radius: px(8),
        click_func: () => {
          this.selectBase(baseConfig.base, baseConfig.name);
        }
      });
    });

    // 关闭按钮
    createWidget(widget.BUTTON, {
      x: px(200),
      y: px(360),
      w: px(80),
      h: px(32),
      text: '关闭',
      text_size: px(14),
      color: 0xffffff,
      normal_color: 0x666666,
      press_color: 0x888888,
      radius: px(8),
      click_func: () => {
        this.hideBaseSelector();
      }
    });
  },

  // ========== 核心功能方法 - 严格按照HTML原型逻辑 ==========

  // 键盘展开/收起切换 - 完全按照原型逻辑
  toggleKeyboardExpansion() {
    keyboardExpanded = !keyboardExpanded;

    if (keyboardExpanded) {
      // 展开键盘
      uiComponents.keyboardContainer.setProperty(prop.Y, px(160));
      uiComponents.resultsSection.setProperty(prop.H, px(140));
      uiComponents.keyboardHint.setProperty(prop.VISIBLE, false);
    } else {
      // 收起键盘
      uiComponents.keyboardContainer.setProperty(prop.Y, px(400));
      uiComponents.resultsSection.setProperty(prop.H, px(220));
      uiComponents.keyboardHint.setProperty(prop.VISIBLE, true);
    }
  },

  // 输入数字/字母 - 完全按照原型逻辑
  inputDigit(digit) {
    if (currentInput === '0') {
      currentInput = digit;
    } else {
      currentInput += digit;
    }
    this.updateDisplay();
    this.convertAllBases();
  },

  // 删除字符 - 完全按照原型逻辑
  deleteDigit() {
    if (currentInput.length > 1) {
      currentInput = currentInput.slice(0, -1);
    } else {
      currentInput = '0';
    }
    this.updateDisplay();
    this.convertAllBases();
  },

  // 清空输入 - 完全按照原型逻辑
  clearInput() {
    currentInput = '0';
    this.updateDisplay();
    this.convertAllBases();
  },

  // 更新显示 - 完全按照原型逻辑
  updateDisplay() {
    uiComponents.inputDisplay.setProperty(prop.TEXT, currentInput);
  },

  // 转换所有进制 - 完全按照原型逻辑
  convertAllBases() {
    const results = convertAll(currentInput, currentBase);
    if (!results) {
      // 输入无效
      return;
    }

    // 更新各进制结果显示 - 按照原型的bases顺序
    const bases = [2, 8, 10, 16, 36, 62];
    bases.forEach(base => {
      if (uiComponents.resultItems[base]) {
        uiComponents.resultItems[base].setProperty(prop.TEXT, results[base]);
      }
    });
  },

  // 显示进制选择器 - 完全按照原型逻辑
  showBaseSelector() {
    uiComponents.baseSelector.setProperty(prop.VISIBLE, true);
  },

  // 隐藏进制选择器 - 完全按照原型逻辑
  hideBaseSelector() {
    uiComponents.baseSelector.setProperty(prop.VISIBLE, false);
  },

  // 选择进制 - 完全按照原型逻辑
  selectBase(base, name) {
    currentBase = base;
    currentBaseName = name;
    uiComponents.currentBaseDisplay.setProperty(prop.TEXT, `当前: ${name} (${base})`);

    this.hideBaseSelector();
    this.convertAllBases();
  },

  // 更新键盘显示 - 简化版本，只更新模式指示器
  updateKeyboardDisplay() {
    // 在Zepp OS中，按键创建后不易动态更新文本
    // 所以我们在按键点击时根据当前capsLock状态来决定输入的字符
    // 这里只需要确保模式指示器正确显示
    const modeText = capsLock ? '大写' : '小写';
    uiComponents.modeIndicator.setProperty(prop.TEXT, modeText);
  },

  // 切换大小写 - 完全按照原型逻辑
  toggleCapsLock() {
    capsLock = !capsLock;
    const modeText = capsLock ? '大写' : '小写';
    uiComponents.modeIndicator.setProperty(prop.TEXT, modeText);
    this.updateKeyboardDisplay();
  },

  // 触觉反馈
  vibrate() {
    if (vibrator) {
      try {
        vibrator.start();
        setTimeout(() => {
          vibrator.stop();
        }, 50);
      } catch (error) {
        console.log('振动失败:', error);
      }
    }
  }

});

  // 创建键盘
  createKeyboard() {
    // 创建键盘容器背景
    uiComponents.keyboardContainer = createWidget(widget.FILL_RECT, {
      ...Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER,
      ...Layout.KEYBOARD_STYLES.CONTAINER
    });

    // 创建键盘拉手
    this.createKeyboardHandle();

    // 创建键盘按键
    this.createKeyboardKeys();
  },

  // 创建键盘拉手
  createKeyboardHandle() {
    const handleY = Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y + px(28);

    // 拉手背景
    createWidget(widget.FILL_RECT, {
      x: px(220),
      y: handleY,
      ...Layout.KEYBOARD_STYLES.HANDLE
    });

    // 拉手点击区域
    const handleClickArea = createWidget(widget.BUTTON, {
      x: px(190),
      y: Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y,
      w: px(100),
      h: px(60),
      color: 0x000000,
      text: '',
      click_func: () => {
        this.toggleKeyboard();
      }
    });
  },

  // 创建键盘按键
  createKeyboardKeys() {
    const keyboardY = Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y + px(60);
    const keyWidth = px(36);
    const keyHeight = px(40);
    const keyGap = px(6);

    // 数字行
    this.createNumberRow(keyboardY, keyWidth, keyHeight, keyGap);

    // 字母行
    this.createLetterRows(keyboardY + px(48), keyWidth, keyHeight, keyGap);

    // 功能键行
    this.createFunctionRow(keyboardY + px(192), keyWidth, keyHeight, keyGap);
  },

  // 创建数字行
  createNumberRow(startY, keyWidth, keyHeight, keyGap) {
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    const startX = px(20);

    numbers.forEach((num, index) => {
      const keyX = startX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY, keyWidth, keyHeight, num, 'input');
    });

    // 删除键
    const deleteX = startX + 10 * (keyWidth + keyGap);
    this.createKey(deleteX, startY, keyWidth, keyHeight, '⌫', 'delete', Layout.KEYBOARD_STYLES.KEY_DELETE);
  },

  // 创建字母行
  createLetterRows(startY, keyWidth, keyHeight, keyGap) {
    const letters = this.getLettersByMode();

    // 第一行: QWERTYUIOP
    const row1 = letters.slice(0, 10);
    const row1StartX = px(40);
    row1.forEach((letter, index) => {
      const keyX = row1StartX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY, keyWidth, keyHeight, letter, 'input');
    });

    // 第二行: ASDFGHJKL
    const row2 = letters.slice(10, 19);
    const row2StartX = px(58);
    row2.forEach((letter, index) => {
      const keyX = row2StartX + index * (keyWidth + keyGap);
      this.createKey(keyX, startY + px(48), keyWidth, keyHeight, letter, 'input');
    });

    // 第三行: ↑ZXCVBNM
    const row3StartX = px(40);
    // 大小写切换键
    this.createKey(row3StartX, startY + px(96), keyWidth, keyHeight, '↑', 'caps',
      appState.capsLock ? { ...Layout.KEYBOARD_STYLES.KEY_CAPS, color: 0xFFC107 } : Layout.KEYBOARD_STYLES.KEY_CAPS);

    // 第三行字母
    const row3 = letters.slice(19, 26);
    row3.forEach((letter, index) => {
      const keyX = row3StartX + (index + 1) * (keyWidth + keyGap);
      this.createKey(keyX, startY + px(96), keyWidth, keyHeight, letter, 'input');
    });
  },

  // 创建功能键行
  createFunctionRow(startY, keyWidth, keyHeight, keyGap) {
    // 进制选择键
    this.createKey(px(80), startY, px(200), keyHeight, '进制选择', 'base-select', Layout.KEYBOARD_STYLES.KEY_SPECIAL);

    // 清空键
    this.createKey(px(300), startY, px(100), keyHeight, '清空', 'clear', Layout.KEYBOARD_STYLES.KEY_DELETE);
  },

  // 创建单个按键
  createKey(x, y, width, height, text, action, customStyle = {}) {
    const keyStyle = { ...Layout.KEYBOARD_STYLES.KEY, ...customStyle };

    const keyButton = createWidget(widget.BUTTON, {
      x: x,
      y: y,
      w: width,
      h: height,
      radius: keyStyle.radius,
      normal_color: keyStyle.color,
      press_color: this.adjustColor(keyStyle.color, 0.8),
      text: text,
      text_size: keyStyle.text_size || px(16),
      color: keyStyle.text_color || 0xffffff,
      click_func: () => {
        this.handleKeyPress(action, text);
      }
    });

    return keyButton;
  },

  // 调整颜色亮度
  adjustColor(color, factor) {
    const r = Math.floor(((color >> 16) & 0xFF) * factor);
    const g = Math.floor(((color >> 8) & 0xFF) * factor);
    const b = Math.floor((color & 0xFF) * factor);
    return (r << 16) | (g << 8) | b;
  },

  // 获取当前模式的字母
  getLettersByMode() {
    const upperLetters = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
    const lowerLetters = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
    return appState.capsLock ? upperLetters : lowerLetters;
  },

  // 处理按键事件
  handleKeyPress(action, text) {
    // 触觉反馈
    this.vibrate();

    switch (action) {
      case 'input':
        this.inputCharacter(text);
        break;
      case 'delete':
        this.deleteCharacter();
        break;
      case 'clear':
        this.clearInput();
        break;
      case 'caps':
        this.toggleCapsLock();
        break;
      case 'base-select':
        this.showBaseSelector();
        break;
    }
  },

  // 输入字符
  inputCharacter(char) {
    // 验证字符是否在当前进制下有效
    if (!baseConverter.isValidInput(char, appState.currentBase)) {
      console.log(`字符 ${char} 在 ${appState.currentBase} 进制下无效`);
      return;
    }

    // 如果当前输入是 '0'，替换而不是追加
    if (appState.currentInput === '0') {
      appState.currentInput = char;
    } else {
      appState.currentInput += char;
    }

    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 删除字符
  deleteCharacter() {
    if (appState.currentInput.length > 1) {
      appState.currentInput = appState.currentInput.slice(0, -1);
    } else {
      appState.currentInput = '0';
    }

    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 清空输入
  clearInput() {
    appState.currentInput = '0';
    this.updateInputDisplay();
    this.updateAllResults();
  },

  // 切换大小写
  toggleCapsLock() {
    appState.capsLock = !appState.capsLock;
    this.updateModeIndicator();
    // 重新创建键盘以更新字母显示
    this.recreateKeyboard();
  },

  // 显示进制选择器
  showBaseSelector() {
    // 这里可以实现进制选择弹窗
    // 暂时使用简单的循环切换常用进制
    const currentIndex = COMMON_BASES.findIndex(base => base.base === appState.currentBase);
    const nextIndex = (currentIndex + 1) % COMMON_BASES.length;
    const nextBase = COMMON_BASES[nextIndex];

    this.changeBase(nextBase.base, nextBase.name);
  },

  // 切换进制
  changeBase(newBase, newBaseName) {
    appState.currentBase = newBase;
    appState.currentBaseName = newBaseName;

    this.updateCurrentBaseDisplay();
    this.updateAllResults();
    this.recreateKeyboard(); // 重新创建键盘以更新有效按键
  },

  // 切换键盘展开状态
  toggleKeyboard() {
    appState.keyboardExpanded = !appState.keyboardExpanded;

    if (appState.keyboardExpanded) {
      this.expandKeyboard();
    } else {
      this.collapseKeyboard();
    }
  },

  // 展开键盘
  expandKeyboard() {
    // 移动键盘容器位置
    uiComponents.keyboardContainer.setProperty(prop.Y, px(160));

    // 调整结果显示区域
    uiComponents.resultsBackground.setProperty(prop.H, px(140));
  },

  // 收起键盘
  collapseKeyboard() {
    // 移动键盘容器位置
    uiComponents.keyboardContainer.setProperty(prop.Y, Layout.LAYOUT_CONFIG.KEYBOARD_CONTAINER.y);

    // 恢复结果显示区域
    uiComponents.resultsBackground.setProperty(prop.H, Layout.LAYOUT_CONFIG.RESULTS_SECTION.h);
  },

  // 更新输入显示
  updateInputDisplay() {
    const displayText = baseConverter.formatDisplay(appState.currentInput, appState.currentBase);
    uiComponents.inputDisplay.setProperty(prop.TEXT, displayText);
  },

  // 更新当前进制显示
  updateCurrentBaseDisplay() {
    const text = `当前: ${appState.currentBaseName} (${appState.currentBase})`;
    uiComponents.currentBaseDisplay.setProperty(prop.TEXT, text);
  },

  // 更新模式指示器
  updateModeIndicator() {
    const text = appState.capsLock ? '大写' : '小写';
    uiComponents.modeIndicator.setProperty(prop.TEXT, text);
  },

  // 更新所有转换结果
  updateAllResults() {
    const results = baseConverter.convertToMultiple(
      appState.currentInput,
      appState.currentBase,
      COMMON_BASES.map(base => base.base)
    );

    if (results) {
      appState.results = results;
      this.updateResultsDisplay();
    }
  },

  // 更新结果显示
  updateResultsDisplay() {
    uiComponents.resultItems.forEach(item => {
      const result = appState.results[item.base];
      if (result !== undefined) {
        const formattedResult = baseConverter.formatDisplay(result, item.base);
        item.valueWidget.setProperty(prop.TEXT, formattedResult);
      }
    });
  },

  // 重新创建键盘
  recreateKeyboard() {
    // 这里应该重新创建键盘，但由于 Zepp OS 的限制，
    // 我们暂时只更新显示相关的内容
    this.updateModeIndicator();
  },

  // 触觉反馈
  vibrate() {
    if (vibrator) {
      try {
        vibrator.start();
        setTimeout(() => {
          vibrator.stop();
        }, 50);
      } catch (error) {
        console.log('振动失败:', error);
      }
    }
  }

});
