# showToast

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

![showToast_image}](https://img-cdn.huami.com/20220927/d53c5278ad075cdabc9bcf4e359d3d5c.jpg)

显示消息提示框。

## 类型

```ts
function showToast(option: Option): void
```

## 参数

### Option

| 属性    | 类型                | 必填 | 默认值 | 说明       | API_LEVEL |
| ------- | ------------------- | ---- | ------ | ---------- | --------- |
| content | <code>string</code> | 是   | -      | 提示的内容 | 2.0       |

## 代码示例

```js
import { showToast } from '@zos/interaction'

showToast({
  content: 'hello world',
})
```
