# readFileSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

返回小程序 `/data` 目录下指定文件的全部内容。

## 类型

```ts
function readFileSync(option: Option): Result
```

## 参数

### Option

| 属性    | 类型                 | 必填 | 默认值 | 说明     | API_LEVEL |
| ------- | -------------------- | ---- | ------ | -------- | --------- |
| path    | <code>string</code>  | 是   | -      | 文件路径 | 2.0       |
| options | <code>Options</code> | 否   | -      | 其他选项 | 2.0       |

### Options

| 属性     | 类型                | 必填 | 默认值 | 说明                                          | API_LEVEL |
| -------- | ------------------- | ---- | ------ | --------------------------------------------- | --------- |
| encoding | <code>string</code> | 否   | -      | 当指定了编码方式之后，API 返回结果为 `string` | 2.0       |

### Result

| 类型                                                | 说明                                               |
| --------------------------------------------------- | -------------------------------------------------- |
| <code>ArrayBuffer&#124;string&#124;undefined</code> | 文件内容。如果返回 `undefined`，则表明读取文件失败 |

## 代码示例

```js
import { readFileSync } from '@zos/fs'

const contentBuffer = readFileSync({
  path: 'test.txt',
})

const contentString = readFileSync({
  path: 'test.txt',
  options: {
    encoding: 'utf8',
  },
})
```
