---
title: hmFS.SysProSetInt(key, val)
sidebar_label: SysProSetInt
---

存储临时整数，系统重启将清除

## 类型

```ts
(key: string, val: number) => result
```

## 参数

| 参数 | 说明       | 必填 | 类型     | 默认值 |
| ---- | ---------- | ---- | -------- | ------ |
| key  | 键字符串   | 是   | `string` | -      |
| val  | 存储的整数 | 是   | `number` | -      |

### result

| 说明                   | 类型     |
| ---------------------- | -------- |
| 操作结果，`0` 表示成功 | `number` |

## 用法

```js
hmFS.SysProSetInt('js_test_int', 100)
console.log(hmFS.SysProGetInt('js_test_int'))
```
