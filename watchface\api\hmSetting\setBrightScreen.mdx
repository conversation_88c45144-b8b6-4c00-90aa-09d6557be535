---
title: hmSetting.setBrightScreen(brightTime)
sidebar_label: setBrightScreen
---

设置亮屏时间。需要屏幕常亮时，可设置一个较大的亮屏时间。

## 类型

```ts
(brightTime: number) => result
```

## 参数

### brightTime

| 说明                                            | 必填 | 类型     | 默认值 |
| ----------------------------------------------- | ---- | -------- | ------ |
| 当前设备的亮屏时间，单位为秒，范围 [1, 2147483] | 是   | `number` | -      |

### result

| 说明                       | 类型     |
| -------------------------- | -------- |
| 操作结果，`0` 表示设置成功 | `number` |

## 代码示例

```js
const result = hmSetting.setBrightScreen(10)
```
