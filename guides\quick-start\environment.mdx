---
title: 1. 环境准备
sidebar_label: 1. 环境准备
---

在本章节中，我们将完成以下环境准备工作。

- 安装 Node.js (v >= 16.0)
- 安装 Zeus CLI

## 安装 Node.js

访问 [Node.js 官网 - 下载 Node.js](https://nodejs.org/zh-cn/download/package-manager)。

推荐通过 `nvm` 包管理器的方式进行安装，以下安装指引摘自 `Node.js` 官网。

```sh
# installs nvm (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# download and install Node.js (you may need to restart the terminal)
nvm install 20

# verifies the right Node.js version is in the environment
node -v # should print `v20.15.0`

# verifies the right NPM version is in the environment
npm -v # should print `10.7.0`
```

:::info

- 在 Windows 中我们推荐使用系统内置的 `PowerShell` 终端来执行命令，以管理员方式运行
- 在 MacOS 和 Linux 中推荐使用系统内置的 `Terminal` 终端来执行命令

:::

第一步，执行官网安装指引中的第一条命令，安装 nvm (Node Version Manager)，将命令复制到终端中，键盘按下「回车键」。

```js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
```

第二步，执行官网安装指引中的第二条命令，安装 Node.js，建议直接安装 `lts` 版本。

在 **Windows** 终端依次执行以下两条命令：

```sh
nvm install lts
nvm use lts
```

在 **MacOS** 和 **Linux** 的终端依次执行以下两条命令：

```sh
nvm install --lts
nvm use --lts
```

第三步，检查 Node.js 是否安装成功，在终端执行以下命令，如果终端显示如 `v20.15.0` 这样的版本号，则表明安装成功。

```sh
node -v
```

## 安装 Zeus CLI

[Zeus CLI](../tools/cli/index.md) 是一个 Zepp OS 命令行工具集，我们用它来创建、预览编译以及构建 Zepp OS 小程序或者表盘项目。

第一步，在终端执行安装命令。

```sh
npm i @zeppos/zeus-cli -g
```

:::tip
安装 Zeus CLI 过程中遇到问题请查看 [环境安装常见问题](../faq/env-setup.md)
:::

第二步，检查 Zeus CLI 是否安装成功，在终端执行以下命令，如果终端显示如 `1.5.20` 的版本号，则表明安装成功。

```sh
zeus -v
```
