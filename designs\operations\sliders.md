---
sidebar_label: 滑块
---

# 滑块  

滑块控件(Sliders，简称滑块)通过让用户在可显示进度的区间内操作来选择一个合适的数值，滑动条的上下两端来反映数值的强弱。  

## 设计原则  

操作有效，基于可穿戴设备的特性，需要保证页面整个区域都可以响应滑动操作，根据不同的设备用户也可以通过旋转表冠或者操作按键来进行调节，从而提高使用效率。  

反馈明确，当用户操作时通过进度、图标、颜色来区分不同状态，给予明确且及时反馈。  

## 连续滑块  

在不要求精准、以主观感觉为主的设置中使用连续滑块，让使用者做出更有意义的调整，比如音量滑块。  

![Design](/img/design/052637cde1ce296fed51dce7bb2b0d70.png)  

### 视觉规范  

- 音量图标状态与色值伴随滑动对应变化，图标状态分强、中、弱、静音四个等级对应显示，图标色值随滑动匀速变色，变色梯度色值 #000000 - #999999
- 滑块底部色值： color_sys_item bg     点亮色值：color_sys_normal graphic  

![Design](/img/design/6e1eb9ee74bcfb18cee757336c64904d.png)

## 带有可编辑操作滑块  

用于除了可调整数值外还带有其他针对此数值的操作，比如亮度滑块：滑块区域可连续滑动，点击按钮可开启/关闭自动调节屏幕亮度。  

![Design](/img/design/0bbb1e8729fdf1ad1395f806e7cf5dce.png)  

### 视觉规范  

自动屏幕亮度调节按钮底色名称： ①默认 color_sys_item bg      ②点击态按钮整体亮度降低28.6%     ③激活态 color_sys_key  

![Design](/img/design/operable-slider_2.png)