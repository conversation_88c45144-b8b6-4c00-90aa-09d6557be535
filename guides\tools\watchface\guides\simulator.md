---
title: 连接模拟器
sidebar_label: 连接模拟器
---

import useBaseUrl from '@docusaurus/useBaseUrl'

# 连接模拟器

通过连接模拟器，可以很方便地将制作好的表盘进行预览。还可以调整参数，实时地查看数据展示效果。

## 模拟器安装

请参考[模拟器安装](../../simulator/setup.md)

## 模拟器使用

1. 点击 Simulator 按钮，启动模拟器

![simulator_open.png](/img/zh-cn/docs/watchface/lesson/simulator_open.png)

2. 点击页面右上方"模拟器连接"按钮

![watchface_simulator_connect_button.png](/img/zh-cn/docs/watchface/lesson/watchface_simulator_connect_button.png)

3. 在弹出的模拟器窗口中点击"连接"

![watchface_simulator_connect.png](/img/zh-cn/docs/watchface/lesson/watchface_simulator_connect.png)

4. 这时，在 Simulator 页面可以看到我们制作的表盘

![simulator_apps.png](/img/zh-cn/docs/watchface/lesson/simulator_apps.png)

同时出现了此表盘的预览窗口

<p>
  <img src={useBaseUrl('/img/zh-cn/docs/watchface/lesson/simulator_preview.png')} width="300" />
</p>

## 模拟器参数调整

现在，让我们通过调整模拟器参数，预览下表盘展示效果吧。

1. 我们将心率数据调整为 128

![simulator_change_params.png](/img/zh-cn/docs/watchface/lesson/simulator_change_params.png)

2. 可以在模拟器上看到效果

<p>
  <img src={useBaseUrl('/img/zh-cn/docs/watchface/lesson/simulator_change_preview.png')} width="300" />
</p>

[更多模拟器相关操作](../../simulator/index.md)
