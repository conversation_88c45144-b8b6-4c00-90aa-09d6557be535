---
sidebar_label: 内容板式
---
# 内容板式

本章节规定了页面图文内容的排版和布局规则。请根据页面具体需求，选择合适的布局和信息结构，在不同的使用场景下为用户提供舒适高效的阅读体验。

## 类型

- **一段或多段内容、图文混排的内容版式**

![Design](/img/design/e4261b74-0369-4af1-8e3c-35a635d85218.png)  

>① 仅有一个段落文字的布局。
>
>② 拥有多段文字的布局。
>
>③ 多段文字和插图组合的图文布局。

- **在段落文字和图文混排的基础上增加悬浮按钮或胶囊按钮。**

![Design](/img/design/8f89679a-14f0-40fb-a6a2-26ae30a26f1d.png)  

>① 一段内容配合胶囊按钮
>
>② 应用图标配合说明文字和悬浮的按钮。
>
>③ 多段图文功能介绍配合胶囊按钮。

## 视觉规范

- 可以使用一段文字、多段文字、图文混排进行图文布局。
- 对于标题文本，建议控制在3行以内。如果其内容超出3行，建议将内容进行分拆，使用简短的标题和详细内容的组合，以便用户能够快速、准确地获取必要信息。

![Design](/img/design/7892495c-5d5a-42f4-9d59-c6d3c41ca148.png)  

>① 正确，1 行标题，多行内容。
>
>② 错误，只使用标题样式，导致内容过长。

- 对于方形屏幕设备，标题居中对齐，内容左对齐。圆形屏幕设备标题和内容都居中对齐。

![Design](/img/design/a5ff52db-fc28-4bea-a8f0-42f6fb06509a.png)  

>① 方形屏幕设备，多段文本。
>
>② 圆形屏幕设备，一段文本。

![Design](/img/design/936048dc-7c39-41ba-b283-d2f0f1e7bc6d.png)  

>① 方形屏幕设备，图片和文字组合的图文布局。
>
>② 圆形屏幕设备，图片和文字组合的图文布局。

- 可以根据页面内容需要，搭配使用应用图标、插图、文本内容及按钮。

![Design](/img/design/42285af0-9eda-45b9-90fa-fe9aac37db34.png)

>① 单段文本内容+多选项按钮。
>
>② 1段图文使用悬浮按钮。
>
>③ 多段图文使用胶囊按钮。

## 场景用例

- **用于功能介绍或说明**

由 1 段文字或多段文字，图文混排组成，可以仅使用文字或图片配合文字进行应用功能说明。

![Design](/img/design/b74fa457-4e05-4d0e-baf2-03cbd3d20bdd.png)  

>① 圆形显示设备，生理周期功能说明的单段落文本。
>
>② 圆形显示设备，步数和燃脂说明的多段落文本。

![Design](/img/design/a48c034a-d94f-454d-bc6f-b015984fe126.png)  

>① 方形显示设备，生理周期提醒说明的单段落文本。
>
>② 方形显示设备，心率对步数和燃脂说明的多段落文本。

![Design](/img/design/936048dc-7c39-41ba-b283-d2f0f1e7bc6d.png)

>① 方形显示设备，手表清洁和佩戴方式说明，多段图文布局。
>
>② 圆形显示设备，手表清洁和佩戴方式说明，多段图文布局。

- **应用或功能操作引导**

由图文混排和操作按钮组成，对应用进行说明后引导用户进行下一步操作。

![Design](/img/design/e90d5cac-0736-4de1-b1df-cd4b46386d49.png)  

>① 方形屏幕设备，用户协议页面，包含单段的文本内容和多个胶囊按钮。
>
>② 圆形屏幕设备，用户协议页面，包含单段的文本内容和多个胶囊按钮。

![Design](/img/design/085bb0d9-4583-476d-893e-07d958da220d.png)  

>① 圆形屏幕设备，一键测量功能的引导页面，包含功能图标、介绍文字以及悬浮按钮。
>
>② 圆形屏幕设备，身体成分应用欢迎页面，包含图标和名称，带有悬浮按钮。

![Design](/img/design/542fdf1b-205d-4ecb-b63f-f30ff66c4f07.png)  

>① 方形屏幕设备，一键测量功能的引导页面，包含功能图标、介绍文字以及悬浮按钮。
>
>② 方形屏幕设备，身体成分应用欢迎页面，包含图标和名称，带有悬浮按钮。

![Design](/img/design/f3d4ffbd-7ff8-48f9-8579-884467776d10.png)  

>① 圆形屏幕设备，Readiness功能的介绍页面，包含多段图文内容及单个引导按钮。
>
>② 方形屏幕设备，Readiness功能的介绍页面，包含多段图文内容及单个引导按钮。
