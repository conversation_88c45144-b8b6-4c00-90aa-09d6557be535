# AI 开发程序使用指南

本指南专门为 AI 开发程序提供 Zepp OS 中文开发者文档的使用建议。

## 文档集合概述

- **文档版本**: Zepp OS v3 (Current/最新版本)
- **文档语言**: 简体中文
- **文档数量**: 506 个文件
- **覆盖范围**: 完整的 Zepp OS 开发生态

## 快速开始路径

### 1. 必读基础文档 (优先级: 🔥🔥🔥)
```
intro.mdx                           # Zepp OS 开发文档介绍
guides/quick-start.mdx               # 快速上手指南
guides/architecture/folder-structure.mdx  # 项目目录结构
reference/app-json.mdx               # 应用配置文件规范
```

### 2. 核心开发文档 (优先级: 🔥🔥)
```
guides/architecture/life-cycle.mdx   # 应用生命周期
guides/framework/                    # 框架介绍目录
reference/device-app-api/            # 设备应用 API 参考
samples/                            # 示例代码目录
```

### 3. 进阶开发文档 (优先级: 🔥)
```
guides/best-practice/               # 最佳实践
guides/tools/                       # 开发工具
watchface/                          # 表盘开发
designs/                            # 设计规范
```

## 按开发任务分类

### 小程序开发
**核心文档路径:**
- `guides/quick-start.mdx` - 小程序快速开始
- `guides/architecture/` - 架构设计
- `reference/device-app-api/` - 设备端 API
- `reference/side-service-api/` - 伴生服务 API
- `samples/app/` - 小程序示例

**关键 API 文档:**
- `reference/device-app-api/newAPI/ui/` - UI 组件
- `reference/device-app-api/newAPI/sensor/` - 传感器
- `reference/device-app-api/newAPI/fs/` - 文件系统
- `reference/device-app-api/newAPI/i18n/` - 国际化

### 表盘开发
**核心文档路径:**
- `watchface/watchface-quick-start.mdx` - 表盘快速开始
- `watchface/specification.md` - 表盘规范
- `watchface/design-resources.md` - 设计资源
- `samples/watchface/` - 表盘示例

### 设计开发
**核心文档路径:**
- `designs/concept/` - 设计理念
- `designs/elements/` - 界面元素
- `designs/visual/` - 视觉规范
- `designs/interaction/` - 交互设计

## 常用 API 速查

### UI 组件
```
reference/device-app-api/newAPI/ui/widget/TEXT/
reference/device-app-api/newAPI/ui/widget/BUTTON/
reference/device-app-api/newAPI/ui/widget/IMG/
reference/device-app-api/newAPI/ui/widget/ARC/
reference/device-app-api/newAPI/ui/widget/CIRCLE/
```

### 系统功能
```
reference/device-app-api/newAPI/sensor/HeartRate/
reference/device-app-api/newAPI/sensor/Step/
reference/device-app-api/newAPI/sensor/Battery/
reference/device-app-api/newAPI/display/
reference/device-app-api/newAPI/vibrate/
```

### 数据存储
```
reference/device-app-api/newAPI/fs/
reference/device-app-api/newAPI/storage/
reference/side-service-api/api/fetch/
```

## 问题解决路径

### 开发环境问题
```
guides/faq/env-setup.md             # 环境安装问题
guides/faq/simulator-faq.md         # 模拟器问题
guides/tools/cli/                   # CLI 工具使用
```

### 代码问题
```
guides/faq/preview-error.md         # 预览问题
guides/best-practice/               # 最佳实践
samples/                            # 参考示例
```

### 设计问题
```
designs/specifications/             # 设计规范
designs/template/                   # 模板参考
guides/faq/icon-faq.md             # 图标问题
```

## 示例代码参考

### 小程序示例
- `samples/app/calories.md` - 卡路里计算器 (数据展示)
- `samples/app/toDoList.md` - 待办事项 (数据管理)
- `samples/app/fetchAPI.md` - 网络请求 (API 调用)
- `samples/app/showcase.md` - 功能展示 (综合示例)

### 表盘示例
- `samples/watchface/colorWorld.md` - 色彩时间 (基础表盘)
- `samples/watchface/basketball.md` - 篮球主题 (运动表盘)
- `samples/watchface/timer.md` - 计时器 (功能表盘)

## 版本兼容性

当前文档基于 **API_LEVEL 4.0** (最新版本):
- 查看 `guides/version-info/` 了解版本特性
- 参考 `guides/version-info/migration-guide.md` 进行版本升级
- 检查 `reference/related-resources/device-list.mdx` 确认设备支持

## 开发工具链

### 必需工具
- **Zeus CLI**: `guides/tools/cli/` - 命令行工具
- **模拟器**: `guides/tools/simulator/` - 设备模拟器
- **调试工具**: `guides/tools/` - 调试相关

### 推荐工具
- **Figma 组件库**: `designs/download.md` - 设计资源
- **字体资源**: `designs/download.md` - 官方字体

## 最佳实践建议

1. **项目结构**: 严格按照 `guides/architecture/folder-structure.mdx` 组织代码
2. **国际化**: 参考 `guides/best-practice/i18n.mdx` 实现多语言
3. **屏幕适配**: 使用 `guides/best-practice/multi-screen-adaption.mdx` 适配不同设备
4. **性能优化**: 遵循 `guides/best-practice/` 中的性能建议
5. **代码规范**: 参考示例代码的编写风格

## 文档使用技巧

1. **搜索优先**: 使用 `FILE_LIST.txt` 快速定位相关文档
2. **示例驱动**: 先看示例代码，再查阅 API 文档
3. **问题导向**: 遇到问题先查 `guides/faq/` 目录
4. **版本注意**: 确保使用的 API 与目标设备版本兼容

## 联系与支持

- **开发者社区**: `guides/community.md`
- **问题反馈**: `guides/faq/upload-log.md`
- **官方资源**: `reference/related-resources/`

---

**提示**: 本文档集合为 AI 开发程序优化，建议按照优先级顺序阅读，遇到具体问题时再深入相关章节。
