{"configVersion": "v3", "app": {"appId": 25064, "appName": "进制转换器", "appType": "app", "version": {"code": 1, "name": "1.0.0"}, "icon": "icon.png", "vender": "zepp", "description": "支持2-62进制转换的专业计算工具"}, "permissions": ["data:os.device.info", "device:os.local_storage"], "runtime": {"apiVersion": {"compatible": "3.0.0", "target": "3.0.0", "minVersion": "3.0"}}, "debug": false, "targets": {"gt": {"module": {"page": {"pages": ["page/gt/converter/index.page"]}}, "platforms": [{"st": "r"}, {"st": "s"}], "designWidth": 480}}, "i18n": {"en-US": {"appName": "Base Converter"}, "zh-CN": {"appName": "进制转换器"}}, "defaultLanguage": "zh-CN"}