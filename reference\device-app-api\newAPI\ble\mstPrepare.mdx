# mstPrepare

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

prepare 接口。

## 类型

```ts
function mstPrepare(profile: Profile): void
```

## 参数

### Profile

| 类型                | 说明                                    |
| ------------------- | --------------------------------------- |
| <code>number</code> | `mstBuildProfile` 返回的 `profile` 指针 |

## 代码示例

```js
import { mstPrepare } from '@zos/ble'

// ...
```
