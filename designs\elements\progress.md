---
sidebar_label: 进度
---

# 进度

进度指示器通过向用户反馈当前的响应进度和合理的时间消耗，指引用户顺利的完成目标任务，使用户的等待过程变得清晰而流畅。

## 设计目标

- 帮助用户明确当前状态，为用户的后续决策提供充足的支持；
- 向用户传达有根据的等待原因，缓解用户等待时的焦虑感；

## 横向进度条

用于描述某个任务的进度

![Design](/img/design/6a6750a2999f9233e95a7459ec1f6938.png)

![Design](/img/design/67269d299d87a49e5c7bd0efafee8195.png)

## 环形进度条

用于描述某个任务的进度。

![Design](/img/design/bac47c120665cd48f79f026ee8a2625f.png)

环形进度条在非圆形屏幕上为了适应布局，超过一屏的页面会变成横向进度条。

![Design](/img/design/42d31b3d453d0abfe0e7bf6b568e43bf.png)

## 文字进度指示

用于描述某个任务的文字进度。

![Design](/img/design/6e7a8c92054e669ec91aeb659a5f237d.png)

![Design](/img/design/ac91e19ec29a2a5179f49de902f668ea.png)
