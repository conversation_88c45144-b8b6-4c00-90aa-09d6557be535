---
sidebar_label: 语言文字
---

# 语言文字

**默认文字**

使用 Zepp OS 默认字库进行设计和开发，英文为产品无法匹配语言时的默认语言，建议以英文版本为基础进行设计。

![Design](/img/design/b35859212f9625c8211e12297b46a300.png)

**大小写**

涉及到程序转换大小写的情况，务必需要先判断语言类型。不能仅用大小写来传达某种必要信息。

**标点**

注意将标点符号转换为本地方案，并特别注意换行时的标点位置。

系统特殊标点字符

| **名称** | **符号显示** |
| -------- | ------------ |
| 角度     | °            |
| 分       | ′            |
| 秒       | ″            |

**单位**

单位需要本地化处理，仅美国、利比里亚、缅甸使用英制单位 ，其他国家或地区使用公制。中国香港、加拿大混合使用两种单位。部分单位需要注意本地化翻译，比如km。

| **数据** | **公制单位（英文）** | **公制单位（中文）** | **英制单位（英文）** | **英制单位（中文）** |
| -------- | -------------------- | -------------------- | -------------------- | -------------------- |
| 距离     | km                   | 公里                 | mi                   | 英里                 |
| 速度     | km/h                 | 公里/时              | mph                  | 英里/时              |
| 步幅     | cm                   | 厘米                 | in                   | 英寸                 |
| 游泳距离 | m                    | 米                   | yd                   | 码                   |
| 消耗     | kcal                 | 千卡                 | kcal                 | 千卡                 |
| 温度     | ℃                    | 摄氏度               | ℉                    | 华氏度               |

**文字样式**

避免斜体、下划线等文字样式。

![Design](/img/design/094f4479065390555dd2998fd9582ec8.png)
