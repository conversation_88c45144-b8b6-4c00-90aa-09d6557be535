import useBaseUrl from '@docusaurus/useBaseUrl'

# 介绍

「运动扩展」是 Zepp OS 3.5 版本的新特性，以插件的形式扩展系统应用「运动」的功能，旨在提升用户在运动中的体验。

该功能允许开发者使用 Zepp OS JS API 创建「运动扩展」插件，在系统应用「运动」中为用户提供更多功能，包括不限于运动数据、运动图表、指南针、GoPro 等应用。

:::info
「运动扩展」功能在 Amazfit T-Rex 3、Amazfit Cheetah Pro、Amazfit Cheetah (Round)、Amazfit Cheetah Square、Amazfit T-Rex Ultra、Amazfit Falcon 设备上可以体验。
:::

「运动扩展」有以下核心价值：

1. **可定制性**

- **个性化 UI 设计**：开发者可以利用 Zepp OS 的强大控件能力，完全自由地绘制和定制视图。
- **专属运动数据组合**：开发者能够根据个人的运动类型和偏好，设计并选择显示的数据组合，使界面更加贴合特定运动的需求。
- **灵活功能拓展**：无需通过空中下载技术（OTA），用户即可为系统应用「运动」增强功能。

1. **实时且丰富的运动数据**

- **实时关键指标监控**：在锻炼或训练中，开发者可以实时监控心率、速度、距离等关键指标，这有助于他们及时调整运动强度和策略，以优化训练效果。
- **深入的高阶数据分析**：提供更高层次和多维度的运动数据，帮助用户深入理解自己的运动表现，进一步提升运动技能和成绩。

1. **功能扩展性**

- **蓝牙连接能力**：智能手表能够与外部设备如踏频传感器进行连接，获取更全面的数据，从而增强运动分析的准确性和深度。
- **网络功能**：智能手表将能够利用网络连接实时同步数据到云端，进行数据记录，以及更复杂的数据处理和分析。

## 体验运动扩展

### 1. 在应用商店下载「运动扩展」应用

<img src={useBaseUrl('/img/docs/workout-extension/store.png')} width="70%" title="download_zepp" />

### 2. 在运动中添加「运动扩展」

在运动应用中开始任意运动，按照图示操作添加「运动扩展」，此处配置页面为多选，可以同时运行多个「运动扩展」。

<img src={useBaseUrl('/img/docs/workout-extension/add.png')} width="100%" title="download_zepp" />

成功添加「运动扩展」，整个界面由 JS API 绘制，可以展示实时的运动数据。

<img
  src={useBaseUrl('/img/docs/workout-extension/sample_both.jpeg')}
  width="80%"
  title="download_zepp"
/>
