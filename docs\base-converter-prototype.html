<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进制转换器 - Balance 手表原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .watch-container {
            width: 480px;
            height: 480px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 8px solid #333;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .app-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
            position: relative;
        }

        .top-section {
            height: 160px;
            display: flex;
            flex-direction: column;
            z-index: 10;
        }

        .title-bar {
            height: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 24px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
        }

        .title-bar h1 {
            font-size: 18px;
            font-weight: 500;
            color: #fff;
        }

        .input-section {
            height: 96px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 12px;
            backdrop-filter: blur(10px);
        }

        .input-display {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 8px;
            min-height: 32px;
            word-break: break-all;
        }

        .current-base {
            font-size: 14px;
            color: #888;
            cursor: pointer;
            transition: color 0.3s;
        }

        .current-base:hover {
            color: #4CAF50;
        }

        .results-section {
            position: absolute;
            top: 180px;
            left: 20px;
            right: 20px;
            bottom: 340px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 12px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            transition: bottom 0.3s ease;
        }

        .results-section.keyboard-expanded {
            bottom: 60px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-size: 14px;
            color: #888;
            min-width: 80px;
        }

        .result-value {
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            word-break: break-all;
            text-align: right;
            flex: 1;
            margin-left: 12px;
        }

        .keyboard-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 320px;
            background: rgba(0, 0, 0, 0.95);
            border-radius: 20px 20px 0 0;
            backdrop-filter: blur(20px);
            transform: translateY(calc(100% - 60px));
            transition: transform 0.3s ease;
            z-index: 50;
        }

        .keyboard-container.expanded {
            transform: translateY(0);
        }

        .keyboard-handle {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .keyboard-handle-bar {
            width: 40px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            transition: background 0.2s;
        }

        .keyboard-handle:hover .keyboard-handle-bar {
            background: rgba(255, 255, 255, 0.6);
        }

        .keyboard-section {
            height: 260px;
            padding: 20px;
            padding-bottom: 30px;
        }

        .keyboard {
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
            max-width: 420px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .keyboard-row {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .key {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            min-height: 40px;
            flex: 1;
            max-width: 38px;
        }

        .key:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .key:active {
            transform: scale(0.95);
        }

        .key.special {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            font-size: 14px;
        }

        .key.special:hover {
            background: rgba(76, 175, 80, 0.5);
        }

        .key.delete {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
            font-size: 18px;
        }

        .key.delete:hover {
            background: rgba(244, 67, 54, 0.5);
        }

        .key.caps {
            background: rgba(255, 193, 7, 0.3);
            border-color: #FFC107;
            font-size: 14px;
        }

        .key.caps:hover {
            background: rgba(255, 193, 7, 0.5);
        }

        .key.caps.active {
            background: rgba(255, 193, 7, 0.6);
            color: #000;
        }

        .key.wide {
            flex: 1.5;
            max-width: 60px;
        }

        .key.extra-wide {
            flex: 2;
            max-width: 80px;
        }

        .numbers-row {
            justify-content: space-between;
        }

        .numbers-row .key {
            max-width: 36px;
        }

        .function-row {
            justify-content: space-between;
            margin-top: 8px;
        }

        .function-row .key {
            flex: 1;
            max-width: 120px;
            margin: 0 4px;
        }

        .base-selector {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
            z-index: 100;
        }

        .base-option {
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .base-option:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .base-option.selected {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .mode-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(76, 175, 80, 0.3);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: #4CAF50;
        }

        .keyboard-toggle-hint {
            position: absolute;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(76, 175, 80, 0.2);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            color: #4CAF50;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 0.4; }
        }
    </style>
</head>
<body>
    <div class="watch-container">
        <div class="mode-indicator" id="modeIndicator">数字</div>
        <div class="keyboard-toggle-hint" id="keyboardHint">上拉展开键盘</div>
        <div class="app-container">
            <div class="top-section">
                <div class="title-bar">
                    <h1>进制转换器</h1>
                </div>

                <div class="input-section">
                    <div class="input-display" id="inputDisplay">0</div>
                    <div class="current-base" id="currentBase" onclick="showBaseSelector()">当前: 十进制 (10)</div>
                </div>
            </div>

            <div class="results-section" id="resultsSection">
                <div class="result-item">
                    <span class="result-label">二进制:</span>
                    <span class="result-value" id="result-2">0</span>
                </div>
                <div class="result-item">
                    <span class="result-label">八进制:</span>
                    <span class="result-value" id="result-8">0</span>
                </div>
                <div class="result-item">
                    <span class="result-label">十进制:</span>
                    <span class="result-value" id="result-10">0</span>
                </div>
                <div class="result-item">
                    <span class="result-label">十六进制:</span>
                    <span class="result-value" id="result-16">0</span>
                </div>
                <div class="result-item">
                    <span class="result-label">三十六进制:</span>
                    <span class="result-value" id="result-36">0</span>
                </div>
                <div class="result-item">
                    <span class="result-label">六十二进制:</span>
                    <span class="result-value" id="result-62">0</span>
                </div>
            </div>
            
        </div>

        <!-- 可收纳键盘 -->
        <div class="keyboard-container" id="keyboardContainer">
            <div class="keyboard-handle" onclick="toggleKeyboardExpansion()">
                <div class="keyboard-handle-bar"></div>
            </div>
            <div class="keyboard-section">
                <div class="keyboard" id="keyboard">
                    <!-- 数字键盘 -->
                    <div class="keyboard-row numbers-row">
                        <div class="key" onclick="inputDigit('1')">1</div>
                        <div class="key" onclick="inputDigit('2')">2</div>
                        <div class="key" onclick="inputDigit('3')">3</div>
                        <div class="key" onclick="inputDigit('4')">4</div>
                        <div class="key" onclick="inputDigit('5')">5</div>
                        <div class="key" onclick="inputDigit('6')">6</div>
                        <div class="key" onclick="inputDigit('7')">7</div>
                        <div class="key" onclick="inputDigit('8')">8</div>
                        <div class="key" onclick="inputDigit('9')">9</div>
                        <div class="key" onclick="inputDigit('0')">0</div>
                        <div class="key delete" onclick="deleteDigit()">⌫</div>
                    </div>

                    <!-- 第一行字母 QWERTYUIOP -->
                    <div class="keyboard-row">
                        <div class="key" onclick="inputDigit('Q')">Q</div>
                        <div class="key" onclick="inputDigit('W')">W</div>
                        <div class="key" onclick="inputDigit('E')">E</div>
                        <div class="key" onclick="inputDigit('R')">R</div>
                        <div class="key" onclick="inputDigit('T')">T</div>
                        <div class="key" onclick="inputDigit('Y')">Y</div>
                        <div class="key" onclick="inputDigit('U')">U</div>
                        <div class="key" onclick="inputDigit('I')">I</div>
                        <div class="key" onclick="inputDigit('O')">O</div>
                        <div class="key" onclick="inputDigit('P')">P</div>
                    </div>

                    <!-- 第二行字母 ASDFGHJKL -->
                    <div class="keyboard-row">
                        <div class="key" onclick="inputDigit('A')">A</div>
                        <div class="key" onclick="inputDigit('S')">S</div>
                        <div class="key" onclick="inputDigit('D')">D</div>
                        <div class="key" onclick="inputDigit('F')">F</div>
                        <div class="key" onclick="inputDigit('G')">G</div>
                        <div class="key" onclick="inputDigit('H')">H</div>
                        <div class="key" onclick="inputDigit('J')">J</div>
                        <div class="key" onclick="inputDigit('K')">K</div>
                        <div class="key" onclick="inputDigit('L')">L</div>
                    </div>

                    <!-- 第三行字母 ↑ZXCVBNM -->
                    <div class="keyboard-row">
                        <div class="key caps" onclick="toggleCapsLock()" id="capsKey">↑</div>
                        <div class="key" onclick="inputDigit('Z')">Z</div>
                        <div class="key" onclick="inputDigit('X')">X</div>
                        <div class="key" onclick="inputDigit('C')">C</div>
                        <div class="key" onclick="inputDigit('V')">V</div>
                        <div class="key" onclick="inputDigit('B')">B</div>
                        <div class="key" onclick="inputDigit('N')">N</div>
                        <div class="key" onclick="inputDigit('M')">M</div>
                    </div>

                    <!-- 功能键行 -->
                    <div class="keyboard-row function-row">
                        <div class="key special" onclick="toggleKeyboard()">🌐</div>
                        <div class="key special wide" onclick="showBaseSelector()">进制选择</div>
                        <div class="key special" onclick="confirmInput()">✓</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="base-selector" id="baseSelector">
            <h3 style="margin-bottom: 16px; text-align: center;">选择输入进制</h3>
            <div class="base-option selected" onclick="selectBase(10, '十进制')">● 十进制 (10)</div>
            <div class="base-option" onclick="selectBase(2, '二进制')">○ 二进制 (2)</div>
            <div class="base-option" onclick="selectBase(8, '八进制')">○ 八进制 (8)</div>
            <div class="base-option" onclick="selectBase(16, '十六进制')">○ 十六进制 (16)</div>
            <div class="base-option" onclick="selectBase(36, '三十六进制')">○ 三十六进制 (36)</div>
            <div class="base-option" onclick="selectBase(62, '六十二进制')">○ 六十二进制 (62)</div>
            <div class="base-option" onclick="hideBaseSelector()" style="text-align: center; margin-top: 12px; color: #888;">取消</div>
        </div>
    </div>

    <script>
        let currentInput = '0';
        let currentBase = 10;
        let currentBaseName = '十进制';
        let keyboardMode = 'numbers'; // 'numbers' or 'letters'
        let capsLock = false;
        let keyboardExpanded = false;

        const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

        function toggleKeyboardExpansion() {
            const container = document.getElementById('keyboardContainer');
            const resultsSection = document.getElementById('resultsSection');
            const hint = document.getElementById('keyboardHint');

            keyboardExpanded = !keyboardExpanded;

            if (keyboardExpanded) {
                container.classList.add('expanded');
                resultsSection.classList.add('keyboard-expanded');
                hint.style.display = 'none';
            } else {
                container.classList.remove('expanded');
                resultsSection.classList.remove('keyboard-expanded');
                hint.style.display = 'block';
            }
        }

        function inputDigit(digit) {
            if (currentInput === '0') {
                currentInput = digit;
            } else {
                currentInput += digit;
            }
            updateDisplay();
            convertAll();
        }

        function deleteDigit() {
            if (currentInput.length > 1) {
                currentInput = currentInput.slice(0, -1);
            } else {
                currentInput = '0';
            }
            updateDisplay();
            convertAll();
        }

        function clearInput() {
            currentInput = '0';
            updateDisplay();
            convertAll();
        }

        function updateDisplay() {
            document.getElementById('inputDisplay').textContent = currentInput;
        }

        function toDecimal(value, fromBase) {
            let result = 0;
            for (let i = 0; i < value.length; i++) {
                const digit = chars.indexOf(value[i].toLowerCase());
                if (digit >= fromBase) return NaN;
                result = result * fromBase + digit;
            }
            return result;
        }

        function fromDecimal(decimal, toBase) {
            if (decimal === 0) return '0';
            let result = '';
            while (decimal > 0) {
                result = chars[decimal % toBase] + result;
                decimal = Math.floor(decimal / toBase);
            }
            return result;
        }

        function convertAll() {
            const decimal = toDecimal(currentInput, currentBase);
            if (isNaN(decimal)) {
                // 输入无效
                return;
            }

            const bases = [2, 8, 10, 16, 36, 62];
            bases.forEach(base => {
                const result = fromDecimal(decimal, base);
                const element = document.getElementById(`result-${base}`);
                if (element) {
                    element.textContent = result.toUpperCase();
                }
            });
        }

        function showBaseSelector() {
            document.getElementById('baseSelector').style.display = 'block';
        }

        function hideBaseSelector() {
            document.getElementById('baseSelector').style.display = 'none';
        }

        function selectBase(base, name) {
            currentBase = base;
            currentBaseName = name;
            document.getElementById('currentBase').textContent = `当前: ${name} (${base})`;
            
            // 更新选中状态
            document.querySelectorAll('.base-option').forEach(option => {
                option.classList.remove('selected');
                option.textContent = option.textContent.replace('●', '○');
            });
            event.target.classList.add('selected');
            event.target.textContent = event.target.textContent.replace('○', '●');
            
            hideBaseSelector();
            convertAll();
        }

        function toggleKeyboard() {
            const modeIndicator = document.getElementById('modeIndicator');

            if (keyboardMode === 'numbers') {
                keyboardMode = 'letters';
                modeIndicator.textContent = capsLock ? '大写' : '小写';
            } else {
                keyboardMode = 'numbers';
                modeIndicator.textContent = '数字';
            }
            updateKeyboardDisplay();
        }

        function updateKeyboardDisplay() {
            const keyboard = document.getElementById('keyboard');
            const letters = getLettersByMode();

            keyboard.innerHTML = `
                <!-- 数字键盘 -->
                <div class="keyboard-row numbers-row">
                    <div class="key" onclick="inputDigit('1')">1</div>
                    <div class="key" onclick="inputDigit('2')">2</div>
                    <div class="key" onclick="inputDigit('3')">3</div>
                    <div class="key" onclick="inputDigit('4')">4</div>
                    <div class="key" onclick="inputDigit('5')">5</div>
                    <div class="key" onclick="inputDigit('6')">6</div>
                    <div class="key" onclick="inputDigit('7')">7</div>
                    <div class="key" onclick="inputDigit('8')">8</div>
                    <div class="key" onclick="inputDigit('9')">9</div>
                    <div class="key" onclick="inputDigit('0')">0</div>
                    <div class="key delete" onclick="deleteDigit()">⌫</div>
                </div>

                <!-- 第一行字母 -->
                <div class="keyboard-row">
                    <div class="key" onclick="inputDigit('${letters[0]}')">${letters[0]}</div>
                    <div class="key" onclick="inputDigit('${letters[1]}')">${letters[1]}</div>
                    <div class="key" onclick="inputDigit('${letters[2]}')">${letters[2]}</div>
                    <div class="key" onclick="inputDigit('${letters[3]}')">${letters[3]}</div>
                    <div class="key" onclick="inputDigit('${letters[4]}')">${letters[4]}</div>
                    <div class="key" onclick="inputDigit('${letters[5]}')">${letters[5]}</div>
                    <div class="key" onclick="inputDigit('${letters[6]}')">${letters[6]}</div>
                    <div class="key" onclick="inputDigit('${letters[7]}')">${letters[7]}</div>
                    <div class="key" onclick="inputDigit('${letters[8]}')">${letters[8]}</div>
                    <div class="key" onclick="inputDigit('${letters[9]}')">${letters[9]}</div>
                </div>

                <!-- 第二行字母 -->
                <div class="keyboard-row">
                    <div class="key" onclick="inputDigit('${letters[10]}')">${letters[10]}</div>
                    <div class="key" onclick="inputDigit('${letters[11]}')">${letters[11]}</div>
                    <div class="key" onclick="inputDigit('${letters[12]}')">${letters[12]}</div>
                    <div class="key" onclick="inputDigit('${letters[13]}')">${letters[13]}</div>
                    <div class="key" onclick="inputDigit('${letters[14]}')">${letters[14]}</div>
                    <div class="key" onclick="inputDigit('${letters[15]}')">${letters[15]}</div>
                    <div class="key" onclick="inputDigit('${letters[16]}')">${letters[16]}</div>
                    <div class="key" onclick="inputDigit('${letters[17]}')">${letters[17]}</div>
                    <div class="key" onclick="inputDigit('${letters[18]}')">${letters[18]}</div>
                </div>

                <!-- 第三行字母 -->
                <div class="keyboard-row">
                    <div class="key caps ${capsLock ? 'active' : ''}" onclick="toggleCapsLock()" id="capsKey">↑</div>
                    <div class="key" onclick="inputDigit('${letters[19]}')">${letters[19]}</div>
                    <div class="key" onclick="inputDigit('${letters[20]}')">${letters[20]}</div>
                    <div class="key" onclick="inputDigit('${letters[21]}')">${letters[21]}</div>
                    <div class="key" onclick="inputDigit('${letters[22]}')">${letters[22]}</div>
                    <div class="key" onclick="inputDigit('${letters[23]}')">${letters[23]}</div>
                    <div class="key" onclick="inputDigit('${letters[24]}')">${letters[24]}</div>
                    <div class="key" onclick="inputDigit('${letters[25]}')">${letters[25]}</div>
                </div>

                <!-- 功能键行 -->
                <div class="keyboard-row function-row">
                    <div class="key special" onclick="toggleKeyboard()">🌐</div>
                    <div class="key special wide" onclick="showBaseSelector()">进制选择</div>
                    <div class="key special" onclick="confirmInput()">✓</div>
                </div>
            `;
        }

        function getLettersByMode() {
            const upperLetters = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
            const lowerLetters = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
            return capsLock ? upperLetters : lowerLetters;
        }

        function showLetterKeyboard() {
            const keyboard = document.getElementById('keyboard');
            keyboard.className = 'keyboard letters';

            const letters = capsLock ?
                ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'] :
                ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];

            keyboard.innerHTML = `
                <div class="key" onclick="inputDigit('${letters[0]}')">${letters[0]}</div>
                <div class="key" onclick="inputDigit('${letters[1]}')">${letters[1]}</div>
                <div class="key" onclick="inputDigit('${letters[2]}')">${letters[2]}</div>
                <div class="key" onclick="inputDigit('${letters[3]}')">${letters[3]}</div>

                <div class="key" onclick="inputDigit('${letters[4]}')">${letters[4]}</div>
                <div class="key" onclick="inputDigit('${letters[5]}')">${letters[5]}</div>
                <div class="key" onclick="inputDigit('${letters[6]}')">${letters[6]}</div>
                <div class="key" onclick="inputDigit('${letters[7]}')">${letters[7]}</div>

                <div class="key" onclick="inputDigit('${letters[8]}')">${letters[8]}</div>
                <div class="key" onclick="inputDigit('${letters[9]}')">${letters[9]}</div>
                <div class="key" onclick="inputDigit('${letters[10]}')">${letters[10]}</div>
                <div class="key" onclick="inputDigit('${letters[11]}')">${letters[11]}</div>

                <div class="key" onclick="inputDigit('${letters[12]}')">${letters[12]}</div>
                <div class="key" onclick="inputDigit('${letters[13]}')">${letters[13]}</div>
                <div class="key" onclick="inputDigit('${letters[14]}')">${letters[14]}</div>
                <div class="key" onclick="inputDigit('${letters[15]}')">${letters[15]}</div>

                <div class="key" onclick="inputDigit('${letters[16]}')">${letters[16]}</div>
                <div class="key" onclick="inputDigit('${letters[17]}')">${letters[17]}</div>
                <div class="key" onclick="inputDigit('${letters[18]}')">${letters[18]}</div>
                <div class="key special" onclick="toggleCapsLock()">${capsLock ? '小写' : '大写'}</div>

                <div class="key" onclick="inputDigit('${letters[19]}')">${letters[19]}</div>
                <div class="key" onclick="inputDigit('${letters[20]}')">${letters[20]}</div>
                <div class="key" onclick="inputDigit('${letters[21]}')">${letters[21]}</div>
                <div class="key" onclick="inputDigit('${letters[22]}')">${letters[22]}</div>

                <div class="key" onclick="inputDigit('${letters[23]}')">${letters[23]}</div>
                <div class="key" onclick="inputDigit('${letters[24]}')">${letters[24]}</div>
                <div class="key" onclick="inputDigit('${letters[25]}')">${letters[25]}</div>
                <div class="key delete" onclick="deleteDigit()">←</div>

                <div class="key special" onclick="toggleKeyboard()">123</div>
                <div class="key wide" onclick="inputDigit(' ')">空格</div>
                <div class="key delete" onclick="clearInput()">清空</div>
            `;
        }

        function toggleCapsLock() {
            capsLock = !capsLock;
            const modeIndicator = document.getElementById('modeIndicator');
            modeIndicator.textContent = capsLock ? '大写' : '小写';
            showLetterKeyboard();
        }

        // 初始化
        convertAll();
    </script>
</body>
</html>
