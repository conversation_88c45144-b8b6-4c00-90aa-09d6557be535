---
sidebar_label: 设计原则
---

# 设计原则

设计原则是践行设计价值观过程中更为具体可操作的向导和提示，它是设计方案过程中的基准以及设计限制。

## 友好 \| Friendly

友好反馈，置界面于用户控制之下，通过情感化设计，提供适度反馈，安抚用户负面情感，强化用户正面情感；

友好指引，适时的通知和指南，帮助用户更好地完成任务解决问题；

友好共通，需要注意设计的包容性，保证无障碍设计和国际化设计，在为大多数用户设计同时关注重点特殊人群、特殊场景下的使用体验，拓展产品服务人群，让更多的用户能够轻松、高效的使用产品。

## 轻量 \| Light

轻界面，时刻保持⻚⾯简洁清晰，移除视觉上的冗杂，使⽤户能聚焦真正有⽤的信息。

轻层级，体现元素之间的层级与空间关系，建立用户与界面的认知连续。

轻操作，操作更轻快，认知更自然，增加界面活力。

轻流程，尽量使用短路径设计功能流程，提高功能使用效率。

## 有效 \| Efficient

有效设计，系统设计是分工合作的产物，通过探索设计规律，模块化设计思路，为合作方提供抽象后的规则，组件和模式，增强界面设计的灵活性和可维护性，节约无谓的设计且保持系统一致性，提升设计的效率。

有效使用，时刻关注用户在使用产品时的有效程度，关注用户个性需求，提供用户灵活个性体验，满足多样化场景互动，降低认知和操作成本，让用户更方便快捷的完成相关任务。
