---
sidebar_label: 快捷卡片
---

# 快捷卡片  

快捷卡片是负一屏上展示某个应用核心功能或重要信息的视觉容器。

## 设计原则  

**轻量实用**：通过快捷卡片，让用户能够快速查看功能信息，或通过轻量的交互来完成快捷操作，实现功能直达、减少层级跳转的目的。请提炼用户最关心的信息或功能展示在快捷卡片上，避免过多次要功能或营销信息的展示。

**一目了然**：快捷卡片的核心理念在于让用户一目了然地获取信息，并提供方便快捷的操作。请在有限的卡片空间内，合理排布文本、图片和操作入口，让用户在视线扫描的几秒内即可获取信息或完成操作。

## 构成

快捷卡片的组成元素包含卡片背景、文本、操作、富媒体等。

背景：默认使用系统 `color_sys_item_bg` 颜色背景，也可以使用提供附加含义的自定义颜色或纹理。

![Design](/img/design/6e07088d-5c2b-4200-9c76-496d9f543716.png)

>① 系统默认颜色背景
>
>② 可传达含义的彩色背景
>
>③ 品牌自身颜色和图形信息的纹理背景

文本：标题，副标题和描述文本。

![Design](/img/design/a4502ace-d793-42e0-9aa0-4e6b582a5666.png)

富媒体：图表、图标、音频播放等元素。

![Design](/img/design/897f2682-86eb-4ac3-add4-872e4080c49e.png)

>① 带有图表元素的卡片
>
>② 带有图形元素的卡片
>
>③ 音频播放样式的卡片

操作：按钮、开关、选择器等可操作部件。

![Design](/img/design/23c230ae-33c1-4f57-bee9-e4b1cfcb7df2.png)

>① 带有按钮部件的卡片
>
>② 带有开关部件的卡片
>
>③ 带有选择部件的卡片

## 视觉规范

- 单个卡片最大高度有固定极限值，如果卡片高度超过极限值，当卡片位于屏幕顶部时，顶部文字会显示不完整。另外，单个卡片最小高度为两行文本高度（120 px)。

![Design](/img/design/33c879df-bee4-4dc2-a2c2-877538135818.png)

>① 圆屏设备卡片最大高度
>
>② 方屏设备卡片最大高度

![Design](/img/design/eb303f9b-cd30-4fda-bf7c-f24ac2838ac5.png)

>① 圆屏设备卡片最小高度
>
>② 方屏设备卡片最小高度

- 卡片内容与卡片边缘应留有安全距离（16px），避免内容挤占卡片边缘而使得整体外观变得杂乱。

![Design](/img/design/d3e08c30-baf5-46ab-b52c-c35c850d10b2.png)

![Design](/img/design/24fdc203-eedb-4b8d-b945-9531597df6dc.png)

- 卡片展示空间有限，请聚焦核心内容进行布局排版。使用简洁、概括的文案作为标题、内容来传达相关信息。例如：对于过长的文本内容，请优先对文案进行精简优化，其次可以根据具体使用场景来选择合适的展示方式，例如省略号显示、滚动显示等，详情可参考 [兼容多语言文本](../internationalization/interface-layouts.md#兼容多语言文本)。

![Design](/img/design/05181aa4-7c2c-4169-a74f-a2531105cacc.png)

![Design](/img/design/c3a0649a-3445-4140-a046-6df814275de0.png)

![Design](/img/design/769717d6-0640-4ad7-99ad-9b28e485dee7.gif)

## 版式布局

我们提供了多种内容布局样式，以适应不同的信息展示或功能使用需求。可以结合卡片自身展示的内容类型，参考对应模板布局进行设计或延展。

- 纯文本形式

![Design](/img/design/1de4eb78-e974-4772-a8ff-0ce4281884d5.png)

>① 标题与注释信息排布，例如：日程信息、待办事项

![Design](/img/design/a21b080d-6c9e-4ebe-b60a-2923f13d920f.png)

>② 标题与大数字信息排布，例如：航班信息、花粉过敏指数

- 元素组合形式

![Design](/img/design/302bca11-8797-421a-95a4-201d189d9d82.png)

>③ 文本与图标或图像组合排布，例如：通话记录、天气

![Design](/img/design/5e49b336-f027-49b7-9341-3db2f4b643f0.png)

>④ 文本与图表组合排布，例如：睡眠状态、气压趋势、温度

![Design](/img/design/fccf41dc-6f52-4b15-ab76-6d55116367d0.png)

>⑤ 文本与操作元素排布，例如：呼吸、闹钟、待办事项

![Design](/img/design/0d64c6e9-2bee-41a9-b6cd-2ceac01ba744.png)

>⑥ 纵向同级别信息排布，例如：运动记录、天气指标

![Design](/img/design/334b7648-9a38-4013-a9f0-07ad6559b0b4.png)

>⑦ 横向同级别信息排布，例如：最近运动、今日活动、倒计时

## Figma 设计模版文件链接

[Shortcut Cards Templates](https://www.figma.com/community/file/1372844485569677237)
