/**
 * 虚拟键盘组件
 * 26键专业布局，支持数字、字母、大小写切换
 */

export class VirtualKeyboard {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      onInput: options.onInput || (() => {}),
      onDelete: options.onDelete || (() => {}),
      onClear: options.onClear || (() => {}),
      onBaseSelect: options.onBaseSelect || (() => {}),
      ...options
    };
    
    this.capsLock = false;
    this.isExpanded = false;
    
    this.init();
  }

  init() {
    this.createKeyboard();
    this.updateDisplay();
  }

  createKeyboard() {
    this.container.innerHTML = `
      <div class="keyboard-handle" id="keyboardHandle">
        <div class="keyboard-handle-bar"></div>
        <div class="keyboard-hint">上拉展开键盘</div>
      </div>
      <div class="keyboard-content" id="keyboardContent">
        <!-- 键盘内容将通过 updateDisplay 动态生成 -->
      </div>
    `;

    // 绑定拉手点击事件
    const handle = this.container.querySelector('#keyboardHandle');
    handle.addEventListener('click', () => {
      this.toggle();
    });
  }

  updateDisplay() {
    const content = this.container.querySelector('#keyboardContent');
    const letters = this.getLettersByMode();
    
    content.innerHTML = `
      <!-- 数字键盘行 -->
      <div class="keyboard-row numbers-row">
        ${this.createNumberKeys()}
        <div class="key delete" data-action="delete">⌫</div>
      </div>
      
      <!-- 第一行字母 QWERTYUIOP -->
      <div class="keyboard-row">
        ${letters.slice(0, 10).map(letter => 
          `<div class="key" data-input="${letter}">${letter}</div>`
        ).join('')}
      </div>
      
      <!-- 第二行字母 ASDFGHJKL -->
      <div class="keyboard-row">
        ${letters.slice(10, 19).map(letter => 
          `<div class="key" data-input="${letter}">${letter}</div>`
        ).join('')}
      </div>
      
      <!-- 第三行字母 ↑ZXCVBNM -->
      <div class="keyboard-row">
        <div class="key caps ${this.capsLock ? 'active' : ''}" data-action="caps">↑</div>
        ${letters.slice(19, 26).map(letter => 
          `<div class="key" data-input="${letter}">${letter}</div>`
        ).join('')}
      </div>
      
      <!-- 功能键行 -->
      <div class="keyboard-row function-row">
        <div class="key special extra-wide" data-action="base-select">进制选择</div>
        <div class="key delete" data-action="clear">清空</div>
      </div>
    `;

    this.bindKeyEvents();
  }

  createNumberKeys() {
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    return numbers.map(num => 
      `<div class="key" data-input="${num}">${num}</div>`
    ).join('');
  }

  getLettersByMode() {
    const upperLetters = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
    const lowerLetters = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
    return this.capsLock ? upperLetters : lowerLetters;
  }

  bindKeyEvents() {
    const keys = this.container.querySelectorAll('.key');
    
    keys.forEach(key => {
      key.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleKeyPress(key);
      });
    });
  }

  handleKeyPress(key) {
    const input = key.dataset.input;
    const action = key.dataset.action;

    if (input) {
      // 输入字符
      this.options.onInput(input);
    } else if (action) {
      // 执行动作
      switch (action) {
        case 'delete':
          this.options.onDelete();
          break;
        case 'clear':
          this.options.onClear();
          break;
        case 'caps':
          this.toggleCapsLock();
          break;
        case 'base-select':
          this.options.onBaseSelect();
          break;
      }
    }

    // 添加按键反馈效果
    key.classList.add('pressed');
    setTimeout(() => {
      key.classList.remove('pressed');
    }, 150);
  }

  toggleCapsLock() {
    this.capsLock = !this.capsLock;
    this.updateDisplay();
    
    // 触发大小写状态变化回调
    if (this.options.onCapsChange) {
      this.options.onCapsChange(this.capsLock);
    }
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
    
    if (this.isExpanded) {
      this.expand();
    } else {
      this.collapse();
    }
  }

  expand() {
    this.container.classList.add('expanded');
    const hint = this.container.querySelector('.keyboard-hint');
    if (hint) {
      hint.style.display = 'none';
    }
    
    // 触发展开回调
    if (this.options.onExpand) {
      this.options.onExpand();
    }
  }

  collapse() {
    this.container.classList.remove('expanded');
    const hint = this.container.querySelector('.keyboard-hint');
    if (hint) {
      hint.style.display = 'block';
    }
    
    // 触发收起回调
    if (this.options.onCollapse) {
      this.options.onCollapse();
    }
  }

  // 获取当前状态
  getState() {
    return {
      capsLock: this.capsLock,
      isExpanded: this.isExpanded
    };
  }

  // 设置状态
  setState(state) {
    if (state.capsLock !== undefined) {
      this.capsLock = state.capsLock;
    }
    if (state.isExpanded !== undefined) {
      this.isExpanded = state.isExpanded;
      if (this.isExpanded) {
        this.expand();
      } else {
        this.collapse();
      }
    }
    this.updateDisplay();
  }

  // 销毁键盘
  destroy() {
    this.container.innerHTML = '';
  }
}
