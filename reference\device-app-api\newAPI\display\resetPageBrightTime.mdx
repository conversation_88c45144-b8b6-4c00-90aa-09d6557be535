# resetPageBrightTime

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

取消 `setPageBrightTime` 设置的亮屏时间。

## 类型

```ts
function resetPageBrightTime(): Result
```

## 参数

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { setPageBrightTime, resetPageBrightTime } from '@zos/display'

setPageBrightTime({
  brightTime: 60000,
})

const result = resetPageBrightTime()
```
