designs/accessibility/color.md
designs/accessibility/contrast-ratio.md
designs/accessibility/index.md
designs/concept/keywords.mdx
designs/concept/overview.mdx
designs/concept/principles.md
designs/concept/values.md
designs/customization/screen-off-mode.md
designs/customization/shortcut-cards.md
designs/customization/watchface.md
designs/customization/widget.md
designs/download.md
designs/elements/charts.md
designs/elements/loading.md
designs/elements/page-indicators.md
designs/elements/progress.md
designs/elements/status-bars.md
designs/elements/text.md
designs/elements/title-bar.md
designs/index.md
designs/input/digit-input.md
designs/input/selectors.md
designs/interaction/definition.md
designs/interaction/digital-crown.md
designs/interaction/physical-buttons.md
designs/interaction/screen-touch-control.md
designs/internationalization/index.md
designs/internationalization/interface-layouts.md
designs/internationalization/languages.md
designs/internationalization/regional-standards.md
designs/internationalization/visual-graphics.md
designs/operations/buttons.md
designs/operations/selection-and-status-indicators.md
designs/operations/sliders.md
designs/operations/switches.md
designs/self-checklist.md
designs/specifications/composition.md
designs/specifications/index.md
designs/specifications/safe-area.md
designs/specifications/screen-off-mode.md
designs/structure/interface-framework.md
designs/structure/page-types.md
designs/structure/switching-pages.md
designs/structure/usage-specifications.md
designs/template/cards.md
designs/template/content-layout.md
designs/template/empty-pages.md
designs/template/list.md
designs/template/pop-up-windows.md
designs/visual/animations.md
designs/visual/color.md
designs/visual/font.md
designs/visual/icons.md
designs/visual/illustrations.md
distribute/index.md
distribute/watchface.md
guides/architecture/arc.mdx
guides/architecture/folder-structure.mdx
guides/best-practice/Basic-environment-construction.mdx
guides/best-practice/bluetooth-communication.mdx
guides/best-practice/code-adaptations-for-new-devices.mdx
guides/best-practice/code-organization.mdx
guides/best-practice/cross-page-communications.mdx
guides/best-practice/debug.mdx
guides/best-practice/error-catch.mdx
guides/best-practice/i18n.mdx
guides/best-practice/multi-screen-adaption.mdx
guides/best-practice/persistence-storage.mdx
guides/best-practice/widget-group.mdx
guides/community.md
guides/community/construction.mdx
guides/community/document-construction.mdx
guides/faq/developer-bridge-mode.md
guides/faq/env-setup.md
guides/faq/icon-faq.md
guides/faq/index.md
guides/faq/join-test-group.md
guides/faq/opus-to-mp3.md
guides/faq/paid-app.md
guides/faq/preview-error.md
guides/faq/re-install-system.md
guides/faq/simulator-4-4k.md
guides/faq/simulator-faq.md
guides/faq/third-party-login.md
guides/faq/upload-log.md
guides/faq/watchface-maker.md
guides/faq/windows-cli-qrcode-error.md
guides/framework/app-settings/intro.md
guides/framework/app-settings/js-support.md
guides/framework/app-settings/register.md
guides/framework/app-settings/ui-intro.md
guides/framework/device/app.md
guides/framework/device/app-service.md
guides/framework/device/ble-central.md
guides/framework/device/compatibility.md
guides/framework/device/fs.md
guides/framework/device/intro.md
guides/framework/device/js-support.md
guides/framework/device/layout.md
guides/framework/device/life-cycle.md
guides/framework/device/page.mdx
guides/framework/device/permission.md
guides/framework/device/router.md
guides/framework/device/screen-adaption.md
guides/framework/device/secondary-widget.md
guides/framework/device/system-event.md
guides/framework/device/system-notification.md
guides/framework/side-service/intro.md
guides/framework/side-service/register.md
guides/quick-start.mdx
guides/quick-start/create.mdx
guides/quick-start/development.mdx
guides/quick-start/environment.mdx
guides/quick-start/preview.mdx
guides/quick-start/simulator-dev.mdx
guides/tools/cli/index.md
guides/tools/cli/release-note.md
guides/tools/npm/index.mdx
guides/tools/npm/officially-recommended.mdx
guides/tools/simulator/download.md
guides/tools/simulator/index.md
guides/tools/simulator/release-note.md
guides/tools/simulator/setup.md
guides/tools/vscode-extension/index.md
guides/tools/vscode-extension/release-note.md
guides/tools/watchface/guides/background.md
guides/tools/watchface/guides/create.md
guides/tools/watchface/guides/date.md
guides/tools/watchface/guides/publish.md
guides/tools/watchface/guides/scanCode.md
guides/tools/watchface/guides/simulator.md
guides/tools/watchface/guides/test.md
guides/tools/watchface/guides/text.md
guides/tools/watchface/guides/time.md
guides/tools/watchface/index.md
guides/tools/watchface/release-note.md
guides/tools/zepp-app.mdx
guides/version-info/migration-guide.md
guides/version-info/new-api.md
guides/version-info/new-features.md
guides/version-info/new-features-21.md
guides/version-info/new-features-30.md
guides/version-info/new-features-35.md
guides/version-info/new-features-40.md
guides/version-info/version-choose.md
guides/workout-extension/distribute.mdx
guides/workout-extension/intro.mdx
guides/workout-extension/quick-start.mdx
INDEX.md
intro.mdx
intro/using.mdx
intro/version.mdx
README-cn.md
reference/app-json.mdx
reference/app-settings-api/global.mdx
reference/app-settings-api/settings-storage.mdx
reference/app-settings-api/ui/auth.mdx
reference/app-settings-api/ui/button.mdx
reference/app-settings-api/ui/image.mdx
reference/app-settings-api/ui/link.mdx
reference/app-settings-api/ui/section.mdx
reference/app-settings-api/ui/select.mdx
reference/app-settings-api/ui/slider.mdx
reference/app-settings-api/ui/text.mdx
reference/app-settings-api/ui/textimagerow.mdx
reference/app-settings-api/ui/textinput.mdx
reference/app-settings-api/ui/toast.mdx
reference/app-settings-api/ui/toggle.mdx
reference/app-settings-api/ui/view.mdx
reference/device-app-api/newAPI/alarm/cancel.mdx
reference/device-app-api/newAPI/alarm/getAllAlarms.mdx
reference/device-app-api/newAPI/alarm/set.mdx
reference/device-app-api/newAPI/app/emitCustomSystemEvent.mdx
reference/device-app-api/newAPI/app/getPackageInfo.mdx
reference/device-app-api/newAPI/app/getPackageInfoById.mdx
reference/device-app-api/newAPI/app/getPerformance.mdx
reference/device-app-api/newAPI/app/getScene.mdx
reference/device-app-api/newAPI/app/queryPermission.mdx
reference/device-app-api/newAPI/app/requestPermission.mdx
reference/device-app-api/newAPI/app-access/getSportData.mdx
reference/device-app-api/newAPI/app-service/exit.mdx
reference/device-app-api/newAPI/app-service/getAllAppServices.mdx
reference/device-app-api/newAPI/app-service/start.mdx
reference/device-app-api/newAPI/app-service/stop.mdx
reference/device-app-api/newAPI/ble/addListener.mdx
reference/device-app-api/newAPI/ble/connectStatus.mdx
reference/device-app-api/newAPI/ble/createConnect.mdx
reference/device-app-api/newAPI/ble/disConnect.mdx
reference/device-app-api/newAPI/ble/mstBuildProfile.mdx
reference/device-app-api/newAPI/ble/mstConnect.mdx
reference/device-app-api/newAPI/ble/mstDestroyProfileInstance.mdx
reference/device-app-api/newAPI/ble/mstDisconnect.mdx
reference/device-app-api/newAPI/ble/mstGetConnIdByRemoteAddr.mdx
reference/device-app-api/newAPI/ble/mstGetProfileInstance.mdx
reference/device-app-api/newAPI/ble/mstOffAllCb.mdx
reference/device-app-api/newAPI/ble/mstOnCharaNotification.mdx
reference/device-app-api/newAPI/ble/mstOnCharaReadComplete.mdx
reference/device-app-api/newAPI/ble/mstOnCharaValueArrived.mdx
reference/device-app-api/newAPI/ble/mstOnCharaWriteComplete.mdx
reference/device-app-api/newAPI/ble/mstOnDescValueArrived.mdx
reference/device-app-api/newAPI/ble/mstOnDescWriteComplete.mdx
reference/device-app-api/newAPI/ble/mstOnPrepare.mdx
reference/device-app-api/newAPI/ble/mstOnServiceChangeBegin.mdx
reference/device-app-api/newAPI/ble/mstOnServiceChangeEnd.mdx
reference/device-app-api/newAPI/ble/mstPair.mdx
reference/device-app-api/newAPI/ble/mstPrepare.mdx
reference/device-app-api/newAPI/ble/mstReadCharacteristic.mdx
reference/device-app-api/newAPI/ble/mstReadDescriptor.mdx
reference/device-app-api/newAPI/ble/mstStartScan.mdx
reference/device-app-api/newAPI/ble/mstStopScan.mdx
reference/device-app-api/newAPI/ble/mstWriteCharacteristic.mdx
reference/device-app-api/newAPI/ble/mstWriteDescriptor.mdx
reference/device-app-api/newAPI/ble/removeListener.mdx
reference/device-app-api/newAPI/ble/send.mdx
reference/device-app-api/newAPI/device/getDeviceInfo.mdx
reference/device-app-api/newAPI/device/getDiskInfo.mdx
reference/device-app-api/newAPI/display/getAutoBrightness.mdx
reference/device-app-api/newAPI/display/getBrightness.mdx
reference/device-app-api/newAPI/display/getSettings.mdx
reference/device-app-api/newAPI/display/pauseDropWristScreenOff.mdx
reference/device-app-api/newAPI/display/pausePalmScreenOff.mdx
reference/device-app-api/newAPI/display/resetDropWristScreenOff.mdx
reference/device-app-api/newAPI/display/resetPageBrightTime.mdx
reference/device-app-api/newAPI/display/resetPalmScreenOff.mdx
reference/device-app-api/newAPI/display/setAutoBrightness.mdx
reference/device-app-api/newAPI/display/setBrightness.mdx
reference/device-app-api/newAPI/display/setPageBrightTime.mdx
reference/device-app-api/newAPI/display/setScreenOff.mdx
reference/device-app-api/newAPI/display/setWakeUpRelaunch.mdx
reference/device-app-api/newAPI/fs/closeSync.mdx
reference/device-app-api/newAPI/fs/mkdirSync.mdx
reference/device-app-api/newAPI/fs/openAssetsSync.mdx
reference/device-app-api/newAPI/fs/openSync.mdx
reference/device-app-api/newAPI/fs/readdirSync.mdx
reference/device-app-api/newAPI/fs/readFileSync.mdx
reference/device-app-api/newAPI/fs/readSync.mdx
reference/device-app-api/newAPI/fs/renameSync.mdx
reference/device-app-api/newAPI/fs/rmSync.mdx
reference/device-app-api/newAPI/fs/statAssetsSync.mdx
reference/device-app-api/newAPI/fs/statSync.mdx
reference/device-app-api/newAPI/fs/writeFileSync.mdx
reference/device-app-api/newAPI/fs/writeSync.mdx
reference/device-app-api/newAPI/global/App.mdx
reference/device-app-api/newAPI/global/AppService.mdx
reference/device-app-api/newAPI/global/AppWidget.mdx
reference/device-app-api/newAPI/global/Buffer.mdx
reference/device-app-api/newAPI/global/clearInterval.mdx
reference/device-app-api/newAPI/global/clearTimeout.mdx
reference/device-app-api/newAPI/global/console.mdx
reference/device-app-api/newAPI/global/DataWidget.mdx
reference/device-app-api/newAPI/global/getApp.mdx
reference/device-app-api/newAPI/global/getCurrentPage.mdx
reference/device-app-api/newAPI/global/Page.mdx
reference/device-app-api/newAPI/global/SecondaryWidget.mdx
reference/device-app-api/newAPI/global/setInterval.mdx
reference/device-app-api/newAPI/global/setTimeout.mdx
reference/device-app-api/newAPI/i18n/getText.mdx
reference/device-app-api/newAPI/interaction/createModal.mdx
reference/device-app-api/newAPI/interaction/offDigitalCrown.mdx
reference/device-app-api/newAPI/interaction/offGesture.mdx
reference/device-app-api/newAPI/interaction/offKey.mdx
reference/device-app-api/newAPI/interaction/onDigitalCrown.mdx
reference/device-app-api/newAPI/interaction/onGesture.mdx
reference/device-app-api/newAPI/interaction/onKey.mdx
reference/device-app-api/newAPI/interaction/onWristMotion.mdx
reference/device-app-api/newAPI/interaction/showToast.mdx
reference/device-app-api/newAPI/media.mdx
reference/device-app-api/newAPI/notification/cancel.mdx
reference/device-app-api/newAPI/notification/getAllNotifications.mdx
reference/device-app-api/newAPI/notification/notify.mdx
reference/device-app-api/newAPI/page/getScrollTop.mdx
reference/device-app-api/newAPI/page/getSwiperIndex.mdx
reference/device-app-api/newAPI/page/scrollTo.mdx
reference/device-app-api/newAPI/page/setScrollLock.mdx
reference/device-app-api/newAPI/page/setScrollMode.mdx
reference/device-app-api/newAPI/page/swipeToIndex.mdx
reference/device-app-api/newAPI/router/back.mdx
reference/device-app-api/newAPI/router/checkSystemApp.mdx
reference/device-app-api/newAPI/router/clearLaunchAppTimeout.mdx
reference/device-app-api/newAPI/router/exit.mdx
reference/device-app-api/newAPI/router/getAppIdByName.mdx
reference/device-app-api/newAPI/router/home.mdx
reference/device-app-api/newAPI/router/launchApp.mdx
reference/device-app-api/newAPI/router/push.mdx
reference/device-app-api/newAPI/router/replace.mdx
reference/device-app-api/newAPI/router/setLaunchAppTimeout.mdx
reference/device-app-api/newAPI/sensor/Accelerometer.mdx
reference/device-app-api/newAPI/sensor/Barometer.mdx
reference/device-app-api/newAPI/sensor/Battery.mdx
reference/device-app-api/newAPI/sensor/BloodOxygen.mdx
reference/device-app-api/newAPI/sensor/BodyTemperature.mdx
reference/device-app-api/newAPI/sensor/Buzzer.mdx
reference/device-app-api/newAPI/sensor/Calorie.mdx
reference/device-app-api/newAPI/sensor/checkSensor.mdx
reference/device-app-api/newAPI/sensor/Compass.mdx
reference/device-app-api/newAPI/sensor/Distance.mdx
reference/device-app-api/newAPI/sensor/FatBurning.mdx
reference/device-app-api/newAPI/sensor/Geolocation.mdx
reference/device-app-api/newAPI/sensor/Gyroscope.mdx
reference/device-app-api/newAPI/sensor/HeartRate.mdx
reference/device-app-api/newAPI/sensor/Pai.mdx
reference/device-app-api/newAPI/sensor/Screen.mdx
reference/device-app-api/newAPI/sensor/Sleep.mdx
reference/device-app-api/newAPI/sensor/Stand.mdx
reference/device-app-api/newAPI/sensor/Step.mdx
reference/device-app-api/newAPI/sensor/Stress.mdx
reference/device-app-api/newAPI/sensor/SystemSounds.mdx
reference/device-app-api/newAPI/sensor/Time.mdx
reference/device-app-api/newAPI/sensor/Vibrator.mdx
reference/device-app-api/newAPI/sensor/Wear.mdx
reference/device-app-api/newAPI/sensor/Weather.mdx
reference/device-app-api/newAPI/sensor/Workout.mdx
reference/device-app-api/newAPI/sensor/WorldClock.mdx
reference/device-app-api/newAPI/settings/getDateFormat.mdx
reference/device-app-api/newAPI/settings/getDistanceUnit.mdx
reference/device-app-api/newAPI/settings/getLanguage.mdx
reference/device-app-api/newAPI/settings/getSleepTarget.mdx
reference/device-app-api/newAPI/settings/getSystemInfo.mdx
reference/device-app-api/newAPI/settings/getSystemMode.mdx
reference/device-app-api/newAPI/settings/getTemperatureUnit.mdx
reference/device-app-api/newAPI/settings/getTimeFormat.mdx
reference/device-app-api/newAPI/settings/getWeightTarget.mdx
reference/device-app-api/newAPI/settings/getWeightUnit.mdx
reference/device-app-api/newAPI/storage/localStorage.mdx
reference/device-app-api/newAPI/storage/sessionStorage.mdx
reference/device-app-api/newAPI/timer/createSysTimer.mdx
reference/device-app-api/newAPI/timer/stopTimer.mdx
reference/device-app-api/newAPI/transfer-file/TransferFile.mdx
reference/device-app-api/newAPI/ui/addEventListener.mdx
reference/device-app-api/newAPI/ui/addLayoutChild.mdx
reference/device-app-api/newAPI/ui/createDialog.mdx
reference/device-app-api/newAPI/ui/createWidget.mdx
reference/device-app-api/newAPI/ui/deleteWidget.mdx
reference/device-app-api/newAPI/ui/getAppWidgetSize.mdx
reference/device-app-api/newAPI/ui/getId.mdx
reference/device-app-api/newAPI/ui/getImageInfo.mdx
reference/device-app-api/newAPI/ui/getProperty.mdx
reference/device-app-api/newAPI/ui/getRtlLayout.mdx
reference/device-app-api/newAPI/ui/gettersetter.mdx
reference/device-app-api/newAPI/ui/getTextLayout.mdx
reference/device-app-api/newAPI/ui/getType.mdx
reference/device-app-api/newAPI/ui/openInspector.mdx
reference/device-app-api/newAPI/ui/redraw.mdx
reference/device-app-api/newAPI/ui/relayoutRtl.mdx
reference/device-app-api/newAPI/ui/removeEventListener.mdx
reference/device-app-api/newAPI/ui/removeLayoutChild.mdx
reference/device-app-api/newAPI/ui/setAlpha.mdx
reference/device-app-api/newAPI/ui/setAppWidgetSize.mdx
reference/device-app-api/newAPI/ui/setEnable.mdx
reference/device-app-api/newAPI/ui/setLayoutParent.mdx
reference/device-app-api/newAPI/ui/setProperty.mdx
reference/device-app-api/newAPI/ui/setStatusBarVisible.mdx
reference/device-app-api/newAPI/ui/showToast.mdx
reference/device-app-api/newAPI/ui/updateLayout.mdx
reference/device-app-api/newAPI/ui/updateLayoutStyle.mdx
reference/device-app-api/newAPI/ui/updateStatusBarTitle.mdx
reference/device-app-api/newAPI/ui/widget/ARC.mdx
reference/device-app-api/newAPI/ui/widget/BUTTON.mdx
reference/device-app-api/newAPI/ui/widget/CANVAS.mdx
reference/device-app-api/newAPI/ui/widget/CHECKBOX_GROUP.mdx
reference/device-app-api/newAPI/ui/widget/CIRCLE.mdx
reference/device-app-api/newAPI/ui/widget/CYCLE_IMAGE_TEXT_LIST.mdx
reference/device-app-api/newAPI/ui/widget/CYCLE_LIST.mdx
reference/device-app-api/newAPI/ui/widget/DIALOG.mdx
reference/device-app-api/newAPI/ui/widget/FILL_RECT.mdx
reference/device-app-api/newAPI/ui/widget/GRADIENT_POLYLINE.mdx
reference/device-app-api/newAPI/ui/widget/GROUP.mdx
reference/device-app-api/newAPI/ui/widget/HISTOGRAM.mdx
reference/device-app-api/newAPI/ui/widget/IMG.mdx
reference/device-app-api/newAPI/ui/widget/IMG_ANIM.mdx
reference/device-app-api/newAPI/ui/widget/KEYBOARD.mdx
reference/device-app-api/newAPI/ui/widget/PAGE_INDICATOR.mdx
reference/device-app-api/newAPI/ui/widget/PAGE_SCROLLBAR.mdx
reference/device-app-api/newAPI/ui/widget/PICK_DATE.mdx
reference/device-app-api/newAPI/ui/widget/PICKER.mdx
reference/device-app-api/newAPI/ui/widget/QRCODE.mdx
reference/device-app-api/newAPI/ui/widget/RADIO_GROUP.mdx
reference/device-app-api/newAPI/ui/widget/SCROLL_LIST.mdx
reference/device-app-api/newAPI/ui/widget/SLIDE_SWITCH.mdx
reference/device-app-api/newAPI/ui/widget/SMART_KEYBOARD.mdx
reference/device-app-api/newAPI/ui/widget/SPORT_DATA.mdx
reference/device-app-api/newAPI/ui/widget/STROKE_RECT.mdx
reference/device-app-api/newAPI/ui/widget/TEXT.mdx
reference/device-app-api/newAPI/ui/widget/VIEW_CONTAINER.mdx
reference/device-app-api/newAPI/ui/widget/VIRTUAL_CONTAINER.mdx
reference/device-app-api/newAPI/ui/widgetAnimations.mdx
reference/device-app-api/newAPI/user/addHealthData.mdx
reference/device-app-api/newAPI/user/getProfile.mdx
reference/device-app-api/newAPI/utils/assets.mdx
reference/device-app-api/newAPI/utils/bufferToString.mdx
reference/device-app-api/newAPI/utils/EventBus.mdx
reference/device-app-api/newAPI/utils/log.mdx
reference/device-app-api/newAPI/utils/px.mdx
reference/device-app-api/newAPI/utils/stringToBuffer.mdx
reference/related-resources/design-resources.mdx
reference/related-resources/device-list.mdx
reference/related-resources/language-list.mdx
reference/related-resources/physical-keys.mdx
reference/revision-history.mdx
reference/side-service-api/download-file.mdx
reference/side-service-api/fetch.mdx
reference/side-service-api/global.mdx
reference/side-service-api/image-convert.mdx
reference/side-service-api/messaging.mdx
reference/side-service-api/settings-storage.mdx
reference/side-service-api/transfer-file.mdx
samples/app/calories.md
samples/app/fetchAPI.md
samples/app/showcase.md
samples/app/toDoList.md
samples/index.md
samples/watchface/basketball.md
samples/watchface/colorWorld.md
samples/watchface/timer.md
watchface/api/hmBle.mdx
watchface/api/hmFS/close.mdx
watchface/api/hmFS/open.mdx
watchface/api/hmFS/open_asset.mdx
watchface/api/hmFS/read.mdx
watchface/api/hmFS/remove.mdx
watchface/api/hmFS/rename.mdx
watchface/api/hmFS/seek.mdx
watchface/api/hmFS/stat.mdx
watchface/api/hmFS/stat_asset.mdx
watchface/api/hmFS/SysProGetBool.mdx
watchface/api/hmFS/SysProGetChars.mdx
watchface/api/hmFS/SysProGetDouble.mdx
watchface/api/hmFS/SysProGetInt.mdx
watchface/api/hmFS/SysProGetInt64.mdx
watchface/api/hmFS/SysProSetBool.mdx
watchface/api/hmFS/SysProSetChars.mdx
watchface/api/hmFS/SysProSetDouble.mdx
watchface/api/hmFS/SysProSetInt.mdx
watchface/api/hmFS/SysProSetInt64.mdx
watchface/api/hmFS/write.mdx
watchface/api/hmSensor/addEventListener.mdx
watchface/api/hmSensor/createSensor.mdx
watchface/api/hmSensor/sensorId/BATTERY.mdx
watchface/api/hmSensor/sensorId/BODY_TEMP.mdx
watchface/api/hmSensor/sensorId/CALORIE.mdx
watchface/api/hmSensor/sensorId/DISTANCE.mdx
watchface/api/hmSensor/sensorId/FAT_BURRING.mdx
watchface/api/hmSensor/sensorId/HEART.mdx
watchface/api/hmSensor/sensorId/MUSIC.mdx
watchface/api/hmSensor/sensorId/PAI.mdx
watchface/api/hmSensor/sensorId/SLEEP.mdx
watchface/api/hmSensor/sensorId/SPO2.mdx
watchface/api/hmSensor/sensorId/STAND.mdx
watchface/api/hmSensor/sensorId/STEP.mdx
watchface/api/hmSensor/sensorId/STRESS.mdx
watchface/api/hmSensor/sensorId/TIME.mdx
watchface/api/hmSensor/sensorId/VIBRATE.mdx
watchface/api/hmSensor/sensorId/WEAR.mdx
watchface/api/hmSensor/sensorId/WEATHER.mdx
watchface/api/hmSensor/sensorId/WORLD_CLOCK.mdx
watchface/api/hmSetting/getBrightness.mdx
watchface/api/hmSetting/getDateFormat.mdx
watchface/api/hmSetting/getDeviceInfo.mdx
watchface/api/hmSetting/getDiskInfo.mdx
watchface/api/hmSetting/getLanguage.mdx
watchface/api/hmSetting/getMileageUnit.mdx
watchface/api/hmSetting/getScreenAutoBright.mdx
watchface/api/hmSetting/getScreenType.mdx
watchface/api/hmSetting/getSleepTarget.mdx
watchface/api/hmSetting/getTimeFormat.mdx
watchface/api/hmSetting/getUserData.mdx
watchface/api/hmSetting/getWeightTarget.mdx
watchface/api/hmSetting/getWeightUnit.mdx
watchface/api/hmSetting/setBrightness.mdx
watchface/api/hmSetting/setBrightScreen.mdx
watchface/api/hmSetting/setBrightScreenCancel.mdx
watchface/api/hmSetting/setScreenAutoBright.mdx
watchface/api/hmSetting/setScreenOff.mdx
watchface/api/hmUI/createWidget.mdx
watchface/api/hmUI/deleteWidget.mdx
watchface/api/hmUI/getProperty.mdx
watchface/api/hmUI/setProperty.mdx
watchface/api/hmUI/widget/ARC.mdx
watchface/api/hmUI/widget/ARC_PROGRESS.mdx
watchface/api/hmUI/widget/BUTTON.mdx
watchface/api/hmUI/widget/CIRCLE.mdx
watchface/api/hmUI/widget/data_type.mdx
watchface/api/hmUI/widget/DATE_POINTER.mdx
watchface/api/hmUI/widget/DELEGATE.mdx
watchface/api/hmUI/widget/edit_watchface.mdx
watchface/api/hmUI/widget/FILL_RECT.mdx
watchface/api/hmUI/widget/GRADKIENT_POLYLINE.mdx
watchface/api/hmUI/widget/IMG.mdx
watchface/api/hmUI/widget/IMG_ANIM.mdx
watchface/api/hmUI/widget/IMG_CLICK.mdx
watchface/api/hmUI/widget/IMG_DATE.mdx
watchface/api/hmUI/widget/IMG_LEVEL.mdx
watchface/api/hmUI/widget/IMG_POINTER.mdx
watchface/api/hmUI/widget/IMG_PROGRESS.mdx
watchface/api/hmUI/widget/IMG_STATUS.mdx
watchface/api/hmUI/widget/IMG_TIME.mdx
watchface/api/hmUI/widget/IMG_WEEK.mdx
watchface/api/hmUI/widget/STROKE_RECT.mdx
watchface/api/hmUI/widget/TEXT.mdx
watchface/api/hmUI/widget/TEXT_IMG.mdx
watchface/api/hmUI/widget/TIME_POINTER.mdx
watchface/api/timer/createTimer.mdx
watchface/api/timer/stopTimer.mdx
watchface/app-json.md
watchface/design-resources.md
watchface/specification.md
watchface/watchface-quick-start.mdx
