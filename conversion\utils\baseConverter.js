/**
 * 进制转换核心工具类 - 严格按照HTML原型实现
 * 完全复刻原型的JavaScript逻辑
 */

// 字符集：完全按照原型
const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

/**
 * 将任意进制转换为十进制 - 完全按照原型逻辑
 * @param {string} value - 输入值
 * @param {number} fromBase - 源进制
 * @returns {number} 十进制结果，无效时返回NaN
 */
export function toDecimal(value, fromBase) {
    let result = 0;
    for (let i = 0; i < value.length; i++) {
        const digit = chars.indexOf(value[i].toLowerCase());
        if (digit >= fromBase) return NaN;
        result = result * fromBase + digit;
    }
    return result;
}

/**
 * 将十进制转换为任意进制 - 完全按照原型逻辑
 * @param {number} decimal - 十进制数
 * @param {number} toBase - 目标进制
 * @returns {string} 转换结果
 */
export function fromDecimal(decimal, toBase) {
    if (decimal === 0) return '0';
    let result = '';
    while (decimal > 0) {
        result = chars[decimal % toBase] + result;
        decimal = Math.floor(decimal / toBase);
    }
    return result;
}

/**
 * 批量转换所有进制 - 完全按照原型逻辑
 * @param {string} currentInput - 当前输入值
 * @param {number} currentBase - 当前进制
 * @returns {Object} 转换结果对象
 */
export function convertAll(currentInput, currentBase) {
    const decimal = toDecimal(currentInput, currentBase);
    if (isNaN(decimal)) {
        // 输入无效
        return null;
    }

    const bases = [2, 8, 10, 16, 36, 62];
    const results = {};

    bases.forEach(base => {
        const result = fromDecimal(decimal, base);
        results[base] = result.toUpperCase();
    });

    return results;
}

/**
 * 获取字母数组 - 完全按照原型逻辑
 * @param {boolean} capsLock - 是否大写锁定
 * @returns {Array} 字母数组
 */
export function getLettersByMode(capsLock) {
    const upperLetters = ['Q','W','E','R','T','Y','U','I','O','P','A','S','D','F','G','H','J','K','L','Z','X','C','V','B','N','M'];
    const lowerLetters = ['q','w','e','r','t','y','u','i','o','p','a','s','d','f','g','h','j','k','l','z','x','c','v','b','n','m'];
    return capsLock ? upperLetters : lowerLetters;
}

/**
 * 获取进制名称映射 - 按照原型的进制选择器
 */
export const baseNames = {
    2: '二进制',
    8: '八进制',
    10: '十进制',
    16: '十六进制',
    36: '三十六进制',
    62: '六十二进制'
};

/**
 * 获取常用进制列表 - 按照原型的进制选择器顺序
 */
export const commonBases = [
    { base: 2, name: '二进制' },
    { base: 8, name: '八进制' },
    { base: 10, name: '十进制' },
    { base: 16, name: '十六进制' },
    { base: 36, name: '三十六进制' },
    { base: 62, name: '六十二进制' }
];
