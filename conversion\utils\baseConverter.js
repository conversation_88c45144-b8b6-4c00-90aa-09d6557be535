/**
 * 进制转换核心工具类
 * 支持 2-62 进制之间的相互转换
 * 优化性能，支持大数运算
 */

export class BaseConverter {
  constructor() {
    // 62进制字符集：0-9, a-z, A-Z
    this.chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    this.charMap = {};
    
    // 构建字符映射表，提高查找效率
    for (let i = 0; i < this.chars.length; i++) {
      this.charMap[this.chars[i]] = i;
    }
  }

  /**
   * 验证输入值在指定进制下是否有效
   * @param {string} value - 输入值
   * @param {number} base - 进制 (2-62)
   * @returns {boolean} 是否有效
   */
  isValidInput(value, base) {
    if (!value || typeof value !== 'string') return false;
    if (base < 2 || base > 62) return false;
    
    const validChars = this.chars.slice(0, base);
    for (let char of value.toLowerCase()) {
      if (!validChars.includes(char)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 将任意进制转换为十进制
   * @param {string} value - 输入值
   * @param {number} fromBase - 源进制
   * @returns {number|null} 十进制结果，无效时返回null
   */
  toDecimal(value, fromBase) {
    if (!this.isValidInput(value, fromBase)) return null;
    
    let result = 0;
    const valueStr = value.toLowerCase();
    
    for (let i = 0; i < valueStr.length; i++) {
      const digit = this.charMap[valueStr[i]];
      if (digit >= fromBase) return null; // 无效字符
      result = result * fromBase + digit;
    }
    
    return result;
  }

  /**
   * 将十进制转换为任意进制
   * @param {number} decimal - 十进制数
   * @param {number} toBase - 目标进制
   * @returns {string} 转换结果
   */
  fromDecimal(decimal, toBase) {
    if (decimal < 0 || toBase < 2 || toBase > 62) return '';
    if (decimal === 0) return '0';
    
    let result = '';
    let num = Math.floor(decimal);
    
    while (num > 0) {
      result = this.chars[num % toBase] + result;
      num = Math.floor(num / toBase);
    }
    
    return result;
  }

  /**
   * 直接进制转换
   * @param {string} value - 输入值
   * @param {number} fromBase - 源进制
   * @param {number} toBase - 目标进制
   * @returns {string|null} 转换结果，失败返回null
   */
  convert(value, fromBase, toBase) {
    const decimal = this.toDecimal(value, fromBase);
    if (decimal === null) return null;
    return this.fromDecimal(decimal, toBase);
  }

  /**
   * 批量转换到多个进制
   * @param {string} value - 输入值
   * @param {number} fromBase - 源进制
   * @param {number[]} toBases - 目标进制数组
   * @returns {Object} 转换结果对象 {base: result}
   */
  convertToMultiple(value, fromBase, toBases) {
    const decimal = this.toDecimal(value, fromBase);
    if (decimal === null) return null;
    
    const results = {};
    for (let base of toBases) {
      results[base] = this.fromDecimal(decimal, base);
    }
    return results;
  }

  /**
   * 获取进制的中文名称
   * @param {number} base - 进制数
   * @returns {string} 中文名称
   */
  getBaseName(base) {
    const names = {
      2: '二进制',
      8: '八进制',
      10: '十进制',
      16: '十六进制',
      32: '三十二进制',
      36: '三十六进制',
      62: '六十二进制'
    };
    return names[base] || `${base}进制`;
  }

  /**
   * 获取常用进制列表
   * @returns {Array} 常用进制配置
   */
  getCommonBases() {
    return [
      { base: 2, name: '二进制', shortName: 'BIN' },
      { base: 8, name: '八进制', shortName: 'OCT' },
      { base: 10, name: '十进制', shortName: 'DEC' },
      { base: 16, name: '十六进制', shortName: 'HEX' },
      { base: 32, name: '三十二进制', shortName: 'B32' },
      { base: 36, name: '三十六进制', shortName: 'B36' },
      { base: 62, name: '六十二进制', shortName: 'B62' }
    ];
  }

  /**
   * 获取指定进制的有效字符集
   * @param {number} base - 进制数
   * @returns {string} 有效字符集
   */
  getValidChars(base) {
    if (base < 2 || base > 62) return '';
    return this.chars.slice(0, base);
  }

  /**
   * 格式化长数值显示（添加分隔符）
   * @param {string} value - 数值字符串
   * @param {number} base - 进制
   * @returns {string} 格式化后的字符串
   */
  formatDisplay(value, base) {
    if (!value || value.length <= 4) return value;
    
    // 二进制每4位分隔，其他进制每3位分隔
    const groupSize = base === 2 ? 4 : 3;
    const separator = ' ';
    
    let result = '';
    for (let i = value.length - 1, count = 0; i >= 0; i--, count++) {
      if (count > 0 && count % groupSize === 0) {
        result = separator + result;
      }
      result = value[i] + result;
    }
    
    return result;
  }
}

// 创建单例实例
export const baseConverter = new BaseConverter();
