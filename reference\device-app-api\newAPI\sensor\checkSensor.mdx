# checkSensor

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

检查传感器在当前设备的可用性。

## 类型

```ts
function checkSensor(sensor: Sensor): Result
```

## 参数

### Sensor

| 类型                | 说明                                                                  |
| ------------------- | --------------------------------------------------------------------- |
| <code>object</code> | 传感器，比如检查定位传感器是否可用，传入 `Geolocation` 传感器构造函数 |

### Result

| 类型                 | 说明                                                            |
| -------------------- | --------------------------------------------------------------- |
| <code>boolean</code> | `true` - 传感器在当前设备可用，`false` - 传感器在当前设备不可用 |

## 代码示例

```js
import { checkSensor, Geolocation } from '@zos/sensor'

const result = checkSensor(Geolocation)
let geolocation = null

if (result) {
  geolocation = new Geolocation()
}
```
