/**
 * Balance 手表圆屏布局 (480x480)
 * 进制转换器界面布局
 */

import { createWidget, widget, align, text_style, event } from '@zos/ui'
import { px } from '@zos/utils'

export const LAYOUT_CONFIG = {
  // 屏幕尺寸
  SCREEN_WIDTH: px(480),
  SCREEN_HEIGHT: px(480),
  
  // 安全区域 (圆屏边缘留白)
  SAFE_MARGIN: px(20),
  
  // 标题栏
  TITLE_BAR: {
    x: px(20),
    y: px(20),
    w: px(440),
    h: px(48)
  },
  
  // 输入显示区
  INPUT_SECTION: {
    x: px(20),
    y: px(80),
    w: px(440),
    h: px(96)
  },
  
  // 输入值显示
  INPUT_DISPLAY: {
    x: px(20),
    y: px(80),
    w: px(440),
    h: px(48)
  },
  
  // 当前进制显示
  CURRENT_BASE: {
    x: px(20),
    y: px(128),
    w: px(300),
    h: px(36)
  },
  
  // 模式指示器
  MODE_INDICATOR: {
    x: px(360),
    y: px(128),
    w: px(100),
    h: px(36)
  },
  
  // 结果显示区
  RESULTS_SECTION: {
    x: px(20),
    y: px(180),
    w: px(440),
    h: px(220) // 默认高度，键盘展开时会调整
  },
  
  // 键盘容器
  KEYBOARD_CONTAINER: {
    x: px(0),
    y: px(400), // 默认收起位置
    w: px(480),
    h: px(320)
  }
};

// 文本样式配置
export const TEXT_STYLES = {
  // 标题样式
  TITLE: {
    text_size: px(24),
    color: 0xffffff,
    align_h: align.CENTER_H,
    align_v: align.CENTER_V,
    text_style: text_style.WRAP
  },
  
  // 输入显示样式
  INPUT_DISPLAY: {
    text_size: px(32),
    color: 0x4CAF50,
    align_h: align.CENTER_H,
    align_v: align.CENTER_V,
    text_style: text_style.ELLIPSIS
  },
  
  // 当前进制样式
  CURRENT_BASE: {
    text_size: px(16),
    color: 0xcccccc,
    align_h: align.LEFT,
    align_v: align.CENTER_V,
    text_style: text_style.WRAP
  },
  
  // 模式指示器样式
  MODE_INDICATOR: {
    text_size: px(14),
    color: 0x2196F3,
    align_h: align.RIGHT,
    align_v: align.CENTER_V,
    text_style: text_style.WRAP
  },
  
  // 结果标签样式
  RESULT_LABEL: {
    text_size: px(16),
    color: 0xcccccc,
    align_h: align.LEFT,
    align_v: align.CENTER_V,
    text_style: text_style.WRAP
  },
  
  // 结果值样式
  RESULT_VALUE: {
    text_size: px(18),
    color: 0xffffff,
    align_h: align.RIGHT,
    align_v: align.CENTER_V,
    text_style: text_style.ELLIPSIS
  }
};

// 颜色配置
export const COLORS = {
  BACKGROUND: 0x000000,
  PRIMARY: 0x4CAF50,
  SECONDARY: 0x2196F3,
  ACCENT: 0xFFC107,
  TEXT_PRIMARY: 0xffffff,
  TEXT_SECONDARY: 0xcccccc,
  TEXT_HINT: 0x888888,
  SURFACE: 0x1a1a1a,
  SURFACE_VARIANT: 0x2d2d2d,
  ERROR: 0xf44336,
  SUCCESS: 0x4CAF50
};

// 创建背景
export function createBackground() {
  return createWidget(widget.FILL_RECT, {
    x: 0,
    y: 0,
    w: LAYOUT_CONFIG.SCREEN_WIDTH,
    h: LAYOUT_CONFIG.SCREEN_HEIGHT,
    color: COLORS.BACKGROUND
  });
}

// 创建标题栏
export function createTitleBar() {
  return createWidget(widget.TEXT, {
    ...LAYOUT_CONFIG.TITLE_BAR,
    ...TEXT_STYLES.TITLE,
    text: '进制转换器'
  });
}

// 创建输入显示
export function createInputDisplay() {
  return createWidget(widget.TEXT, {
    ...LAYOUT_CONFIG.INPUT_DISPLAY,
    ...TEXT_STYLES.INPUT_DISPLAY,
    text: '0'
  });
}

// 创建当前进制显示
export function createCurrentBase() {
  return createWidget(widget.TEXT, {
    ...LAYOUT_CONFIG.CURRENT_BASE,
    ...TEXT_STYLES.CURRENT_BASE,
    text: '当前: 十进制 (10)'
  });
}

// 创建模式指示器
export function createModeIndicator() {
  return createWidget(widget.TEXT, {
    ...LAYOUT_CONFIG.MODE_INDICATOR,
    ...TEXT_STYLES.MODE_INDICATOR,
    text: '小写'
  });
}

// 创建结果显示背景
export function createResultsBackground() {
  return createWidget(widget.FILL_RECT, {
    ...LAYOUT_CONFIG.RESULTS_SECTION,
    radius: px(16),
    color: 0x0d1117
  });
}

// 创建单个结果项
export function createResultItem(index, label, value) {
  const itemHeight = px(32);
  const itemY = LAYOUT_CONFIG.RESULTS_SECTION.y + px(12) + index * (itemHeight + px(4));
  
  const labelWidget = createWidget(widget.TEXT, {
    x: LAYOUT_CONFIG.RESULTS_SECTION.x + px(16),
    y: itemY,
    w: px(120),
    h: itemHeight,
    ...TEXT_STYLES.RESULT_LABEL,
    text: label
  });
  
  const valueWidget = createWidget(widget.TEXT, {
    x: LAYOUT_CONFIG.RESULTS_SECTION.x + px(140),
    y: itemY,
    w: px(284),
    h: itemHeight,
    ...TEXT_STYLES.RESULT_VALUE,
    text: value
  });
  
  return { labelWidget, valueWidget };
}

// 键盘样式配置
export const KEYBOARD_STYLES = {
  CONTAINER: {
    radius: px(20),
    color: 0x1a1a1a
  },
  
  HANDLE: {
    w: px(40),
    h: px(4),
    radius: px(2),
    color: 0x666666
  },
  
  KEY: {
    radius: px(8),
    color: 0x2d2d2d,
    text_size: px(16),
    text_color: 0xffffff
  },
  
  KEY_SPECIAL: {
    color: 0x4CAF50
  },
  
  KEY_DELETE: {
    color: 0xf44336
  },
  
  KEY_CAPS: {
    color: 0xFFC107
  }
};
