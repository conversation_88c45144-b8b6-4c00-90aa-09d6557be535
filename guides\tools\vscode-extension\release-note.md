---
title: 版本历史
---

# 版本历史

### V0.1.2 (2022年08月18日)

#### 📈  优化

- 优化每次使用dev/preview/bridge功能时的设备选择，改为自动读取 STATUS-build 配置。

### V0.1.1 (2022年08月12日)

#### 🔧  修复

- 修复了 bridge 功能下设备选择无效的问题。

#### 📈  优化

- 优化每次使用 dev 功能都需要输入 host 和 port，改为读取 STATUS-simulator 下配置。

### V0.1.0 (2022年08月04日)
#### 🚀  新功能

- zepp os explorer BUILD 支持 bridge 相关功能。

#### 📣  变化

- 此版本起不在依赖需要全局安装 npm 包 `@zeppos/zeus-cli`。
- 此版本起不支持运行模式（terminal和GUI）切换。

### V0.0.3 (2022年06月23日)
#### 🚀  新功能

- zepp os explorer STATUS 视图支持添加自定义变量和行内编辑按钮。
  

### V0.0.2 (2022年06月09日)
#### 🚀  新功能

- 增加zepp-os-explorer视图，用于显示USER、STATUS、BUILD相关信息并提供快捷功能按钮。
- 支持用户定义的设置：1、运行模式的类型；2、底部状态栏显示哪些快捷键。

#### 📈  优化

- 右上角标题状态栏中的小火箭图标🚀 替换成了 ZEPP OS图标。


### V0.0.1 (2022年05月26日)
#### 🚀  新增
  - 实现了 npm 包 [@zeppos/zeus-cli](../../cli/) 命令的可视化交互。
  
