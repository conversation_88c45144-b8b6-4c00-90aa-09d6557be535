# addListener

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册连接状态监听回调函数。

## 类型

```ts
function addListener(callback: Callback): void
```

## 参数

### Callback

| 类型                                        | 说明                            |
| ------------------------------------------- | ------------------------------- |
| <code>(status?: boolean) =&#62; void</code> | 连接回调函数，`status` 连接状态 |

## 代码示例

```js
import { addListener } from '@zos/ble'

// ...
```
