---
title: Image 图片转换
sidebar_label: Image 图片转换
---

由于设备性能受限，「设备应用」不支持直接展示 png 格式的图片，从网络下载的 png 图片需要经过 `image` 模块转换后，才可以在「设备应用」上正常展示。

## image 模块

### convert

将 png 图片转化为「设备应用」支持的图片格式

### 类型

```ts
(options: Options) => Promise<Result>
```

### 参数

### Options: object

| 属性           | 说明                                                                                                                               | 是否必须 | 类型     |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------- | -------- | -------- |
| filePath       | 需要转换格式的图片路径                                                                                                             | 是       | `string` |
| targetFilePath | 如果不填写，转换后图片路径的规则为 `${filePath}_converted`。例如给定 `filePath` 值 `data://1.png`，则转换后的路径为 `data://1.png_converted` | 否       | `string` |

### Result: object

| 属性           | 说明             | 类型           |
| -------------- | ---------------- | -------------- |
| filePath       | 原图片路径       | `string`       |
| targetFilePath | 转换后的图片路径 | `string`       |
| options        | 图片转换信息     | `ResultOption` |

### ResultOption: object

| 属性 | 说明                       | 类型     |
| ---- | -------------------------- | -------- |
| size | 转换后的文件大小，单位字节 | `number` |

## 代码示例

```js
AppSideService({
  onInit() {
    image
      .convert({
        filePath: 'data://download/test.png'
      })
      .then((result) => {
        console.log(reslut.targetFilePath) // data://download/test.png_converted
      })

    image
      .convert({
        filePath: 'data://download/test.png',
        targetFilePath: 'data://download/converted_test.png'
      })
      .then((result) => {
        console.log(reslut.targetFilePath) // data://download/converted_test.pang
      })
  }
})
```
