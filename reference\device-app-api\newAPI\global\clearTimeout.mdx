# clearTimeout

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

取消 `setTimeout` 注册的定时器。

## 类型

```ts
function clearTimeout(timeoutID: TimeoutID): void
```

## 参数

### TimeoutID

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | 定时器的编号 |

## 代码示例

```js
const timeoutID = setTimeout(() => {
  console.log('Hello Zepp OS')
}, 1000)

clearTimeout(timeoutID)
```
