import useBaseUrl from '@docusaurus/useBaseUrl'

# 快速上手

## 介绍

本文会通过一个完整的例子，演示如何创建，开发和测试 Zepp OS 运动扩展应用。

在正式开始前，先介绍一些相关背景：

:::info

- 「运动扩展」必须在 API_LEVEL 3.6 及以上的设备才可以使用
- 「运动扩展」拥有大多数 JS API 的能力，但存在部分限制，详情请参考 [API 限制](#api-限制)
- 「运动扩展」是独立的应用，拥有独立的 appId，需要在开发者后台完成创建，提交，审核流程后才可以在应用商店上线

:::

## 快速起步

### 1. 环境准备

「运动扩展」的开发流程与 Zepp OS 小程序一致，所需的开发工具一致，请参考 [环境准备](../quick-start/environment.mdx) 完成环境准备。

### 2. 创建项目

在创建项目的环节，我们直接使用 [Zeus CLI](../tools/cli/index.md) 工具的 `zeus create workout-sample` 命令创建「运动扩展」模版项目，项目类型选择 `WORKOUT_EXTENSION`

:::note
在执行命令之前请先使用 `npm i @zeppos/zeus-cli -g` 安装 Zeus CLI 的最新版本
:::

### 3. 模拟器运行

此步骤流程与 Zepp OS 小程序一致，参考 [模拟器运行](../quick-start/simulator-dev.mdx) 完成模拟器安装和模拟器运行。

使用终端进入到刚刚创建的「运动扩展」目录下，执行 `zeus dev` 编译预览命令，可以在模拟器中看到运动扩展应用。

<img
  src={useBaseUrl('/img/docs/workout-extension/simulator_dev.jpg')}
  width="90%"
  title="download_zepp"
/>

:::info
为方便开发者开发，运动扩展会在模拟器的应用列表中展示，真机则是需要在系统应用「运动」中添加预览（真机不会在应用列表中显示运动扩展应用）
:::

### 4. 真机预览

此步骤流程与预览 Zepp OS 小程序一致，参考 [真机预览](../quick-start/preview.mdx) 。

## 相关要点

### 「运动扩展」相关 API

- [DataWidget 构造函数](../../reference/device-app-api/newAPI/global/DataWidget.mdx)
- [SPORT_DATA 控件](../../reference/device-app-api/newAPI/ui/widget/SPORT_DATA.mdx)
  - `SPORT_DATA` 控件是为「运动扩展」专门设计的 API，具有丰富的数据项展示类型，可以做到运动数据的实时展示
- [getSportData](../../reference/device-app-api/newAPI/app-access/getSportData.mdx)
  - `getSportData` 接口可以获取到运动应用的实时数据，开发者可以对数据进行处理与记录

### app.json 配置

`app.json` 请参考 [小程序配置](../../reference/app-json.mdx)。

运动扩展的区别仅在 `module` 字段的配置，`data-widget` 代表运动扩展配置。

:::info
「运动扩展」必须在 API_LEVEL `3.6` 及以上的设备才可以使用，`minVersion` 需要配置为 `3.6`。
:::

`widgets` 字段为一个数组，目前最大长度为 `1`，即同一个 appId 的「运动扩展」应用中，最多可以同时存在 `1` 个「运动扩展」插件（后期可能会支持多个）。

```json
"module": {
  "data-widget":{
    "widgets": [{
      "path": "pages/plugin1",
      "window": {
        "isPinned": 1
      },
      "name": "plugin2",
      "icon": "icon2.png",
      "runtime": {
        "ability": [{
          "type": 1,
          "subType": [1, 2, 3]
        }]
      }
    }]
  },
}
```

| 属性    | 类型            | 说明                                                                                      |
| ------- | --------------- | ----------------------------------------------------------------------------------------- |
| type    | `number`        | 目前只支持填写 `1`，代表「运动扩展」类型                                                  |
| subType | `Array<number>` | 支持的运动类型，如果设置空数组 `[]` 代表支持所有运动类型，其余运动类型含义请参考文末 `subType` 运动类型 |

`isPinned` 字段控制顶部悬浮的电量和时间标记是否显示，`0` 显示，`1` 不显示，当设置其不显示时，运动扩展获得整个屏幕的绘图区域。

下图分别为圆形和方形屏幕手表开启运动时的界面，当设置 `isPinned: 0`，可以看到顶部悬浮的电量和时间栏。

<img
  src={useBaseUrl('/img/docs/workout-extension/isPinned_0.png')}
  width="35%"
  title="download_zepp"
/>

<img
  src={useBaseUrl('/img/docs/workout-extension/isPinned_1.png')}
  width="35%"
  title="download_zepp"
/>

在 `480px` 圆形屏幕下，悬浮栏的高度为 `68px`。`454 px` 的圆形屏幕下，悬浮栏的高度进行等比例缩放 `Math.ceil(454/480 * 68) = 64 px`。

在 `390px` 高度的方形屏幕下，悬浮栏的高度为 `60px`。

### 生命周期

运动扩展与 [Zepp OS 小程序生命周期](../../guides/framework/device/life-cycle.md) 基本一致，新增了 `onResume` 和 `onPause` 生命周期。

在介绍生命周期之前，先引入一个概念，叫做「Pause」状态，在这个状态下应用的上下文信息会保留，但是无法响应注册的回调函数，注册的定时器会暂停。

在完成「运动扩展」插件的添加后，触发 `app.js` 的 `onCreate` 生命周期。

随后执行「运动扩展」的 `onInit` 和 `build` 生命周期。之后 `app.js` 进入「Pause」状态，直到进入对应的「运动扩展」。

「运动扩展」有获得焦点的特性，即页面聚焦到「运动扩展」插件时，会触发 `onResume` 生命周期，当「运动扩展」失去焦点时触发 `onPause` 生命周期。

:::info
当「运动扩展」插件失去焦点后，会进入「Pause」 状态。
:::

### API 限制

「运动扩展」在 UI 交互方面存在部分限制。

- 绘制区域有限制，只可以在设备屏幕边界范围内绘制 UI 控件，如「运动扩展」在 Amazfit T-Rex Ultra 设备上的绘图区域大小与屏幕分辨率一致为 454 x 454 px。
- 只能展示单页内容，不支持长页面滚动。
- 不支持滑动操作。页面内不支持 `SCROLL_LIST`、`VIEW_CONTAINER`、`PAGE_INDICATOR`、`SCROLL_BAR` 等支持滑动或者堆叠布局的控件。
- 不支持手势监听。
- 不支持按键响应。
- 支持 `CLICK` 事件。页面内控件支持点击事件。

### 运动扩展 i18n 配置

```json
"i18n": {
  "ar-EG": {
    "data-widget": {
      "widgets": [
        {
          "name": "Running Pace Master"
        }
      ]
    }
  },
  "ca-ES": {
    "data-widget": {
      "widgets": [
        {
          "name": "Running Pace Master"
        }
      ]
    }
  },
}
```

## 完整示例

请看 Zepp Health Github 示例仓库 [workout-extension](https://github.com/zepp-health/zeppos-samples/tree/main/workout-extensions)

<img
  src={useBaseUrl('/img/docs/workout-extension/sample_both.jpeg')}
  width="80%"
  title="download_zepp"
/>

一共提供了三个示例，分别是

- Running Data Assistant（上图左侧）
- Running Pace Master（上图右侧）
- Running Pace Master With Side Service（演示了伴生应用的使用）

## 附录

### `subType` 运动类型值

| `subType` 值 | 运动类型             |
| ------------ | -------------------- |
| `1`          | 户外跑               |
| `2`          | 跑步机（原室内跑）   |
| `3`          | 健走                 |
| `4`          | 户外骑行             |
| `5`          | 自由训练             |
| `6`          | 泳池游泳             |
| `7`          | 公开水域游泳         |
| `8`          | 室内骑行             |
| `9`          | 椭圆机               |
| `10`         | 登山                 |
| `11`         | 越野跑               |
| `12`         | 滑雪                 |
| `15`         | 户外徒步             |
| `17`         | 网球                 |
| `18`         | 足球                 |
| `19`         | 铁人三项             |
| `20`         | 复合运动             |
| `21`         | 跳绳                 |
| `23`         | 划船机               |
| `24`         | 室内健身             |
| `40`         | 室内步行             |
| `41`         | 冰壶                 |
| `42`         | 单板滑雪             |
| `43`         | 高山滑雪             |
| `44`         | 户外滑冰             |
| `45`         | 室内滑冰             |
| `46`         | 越野滑雪             |
| `47`         | 山地骑行             |
| `48`         | 小轮车               |
| `49`         | 高强度间歇训练       |
| `50`         | 核心训练             |
| `51`         | 混合有氧             |
| `52`         | 力量训练             |
| `53`         | 拉伸                 |
| `54`         | 爬楼机               |
| `55`         | 柔韧训练             |
| `57`         | 踏步训练             |
| `58`         | 踏步机               |
| `59`         | 体操                 |
| `60`         | 瑜伽                 |
| `61`         | 普拉提               |
| `62`         | 冲浪                 |
| `63`         | 打猎                 |
| `64`         | 钓鱼                 |
| `65`         | 帆船运动             |
| `66`         | 户外划船             |
| `67`         | 滑板                 |
| `68`         | 桨板冲浪             |
| `69`         | 轮滑                 |
| `70`         | 攀岩                 |
| `71`         | 芭蕾舞               |
| `72`         | 肚皮舞               |
| `73`         | 广场舞               |
| `74`         | 街舞                 |
| `75`         | 交际舞               |
| `76`         | 舞蹈                 |
| `77`         | 尊巴                 |
| `78`         | 板球                 |
| `79`         | 棒球                 |
| `80`         | 保龄球               |
| `81`         | 壁球                 |
| `82`         | 橄榄球               |
| `83`         | 高尔夫               |
| `84`         | 高尔夫挥杆           |
| `85`         | 篮球                 |
| `86`         | 垒球                 |
| `87`         | 门球                 |
| `88`         | 排球                 |
| `89`         | 乒乓球               |
| `90`         | 曲棍球               |
| `91`         | 手球                 |
| `92`         | 羽毛球               |
| `93`         | 射箭                 |
| `94`         | 马术运动             |
| `95`         | 剑术                 |
| `96`         | 空手道               |
| `97`         | 拳击                 |
| `98`         | 柔道                 |
| `99`         | 摔跤                 |
| `100`        | 太极                 |
| `101`        | 泰拳                 |
| `102`        | 跆拳道               |
| `103`        | 武术                 |
| `104`        | 自由搏击             |
| `105`        | 双板滑雪             |
| `106`        | 风筝冲浪             |
| `108`        | 爬楼                 |
| `109`        | 健身操               |
| `110`        | 定向越野             |
| `111`        | 团体操               |
| `112`        | 拉丁舞               |
| `113`        | 爵士舞               |
| `114`        | 搏击操               |
| `115`        | 呼啦圈               |
| `116`        | 飞盘                 |
| `117`        | 飞镖                 |
| `118`        | 放风筝               |
| `119`        | 拔河                 |
| `120`        | 踢毽子               |
| `121`        | 沙滩足球             |
| `122`        | 沙滩排球             |
| `123`        | 漂流                 |
| `124`        | 摩托艇               |
| `125`        | 雪车                 |
| `126`        | 雪橇                 |
| `127`        | 定向滑雪             |
| `128`        | 冬季两项             |
| `129`        | 跑酷                 |
| `130`        | 交叉训练             |
| `131`        | 竞走                 |
| `132`        | 驾车                 |
| `133`        | 滑翔伞               |
| `134`        | 一分钟仰卧起坐       |
| `135`        | 一分钟跳绳           |
| `136`        | 雪地摩托             |
| `137`        | 越野摩托             |
| `138`        | 龙舟                 |
| `139`        | 滑水                 |
| `140`        | 皮划艇               |
| `141`        | 赛艇                 |
| `142`        | 马球                 |
| `143`        | 动感单车             |
| `144`        | 漫步机               |
| `145`        | 墙球                 |
| `146`        | 民族舞               |
| `147`        | 柔术                 |
| `148`        | 击剑                 |
| `149`        | 单杠                 |
| `150`        | 双杠                 |
| `151`        | 台球                 |
| `152`        | 藤球                 |
| `153`        | 躲避球               |
| `154`        | 水球                 |
| `155`        | 蹼泳                 |
| `156`        | 花样游泳             |
| `157`        | 浮潜                 |
| `158`        | 冰球                 |
| `159`        | 秋千                 |
| `160`        | 沙狐球               |
| `161`        | 桌上足球             |
| `162`        | 毽球                 |
| `163`        | 体感游戏             |
| `164`        | 室内足球             |
| `165`        | 嘻哈舞               |
| `166`        | 钢管舞               |
| `167`        | 战绳                 |
| `168`        | 霹雳舞               |
| `169`        | 沙包球               |
| `170`        | 地掷球               |
| `171`        | 回力球               |
| `172`        | 室内冲浪             |
| `173`        | 国际象棋             |
| `174`        | 国际跳棋             |
| `175`        | 围棋                 |
| `176`        | 桥牌                 |
| `177`        | 桌游                 |
| `178`        | 雪鞋健行             |
| `179`        | 射击                 |
| `180`        | 跳伞                 |
| `181`        | 速降                 |
| `182`        | 蹦极                 |
| `183`        | 蹦床                 |
| `184`        | 抱石                 |
| `185`        | 现代舞               |
| `186`        | 迪斯科               |
| `187`        | 踢踏舞               |
| `188`        | 地板球               |
| `189`        | 电子竞技             |
| `190`        | 沙滩车               |
| `191`        | 足球（无 GPS）       |
| `192`        | 操场跑步             |
| `193`        | 钓鱼（钓鱼数量）     |
| `194`        | 室内攀岩             |
| `195`        | 登山滑雪             |
| `196`        | 户外自由潜水         |
| `197`        | 室内自由潜水         |
| `198`        | 渔猎                 |
| `199`        | 简单网球             |
| `200`        | 尾波冲浪             |
| `201`        | 冲浪（识别趟数）     |
| `202`        | 风筝冲浪（识别滑翔） |
| `203`        | 超级马拉松           |
