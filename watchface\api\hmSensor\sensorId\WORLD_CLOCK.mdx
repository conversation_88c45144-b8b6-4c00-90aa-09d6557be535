---
title: WORLD_CLOCK
sidebar_label: WORLD_CLOCK 世界时钟
---

## 创建传感器

```js
const world_clock = hmSensor.createSensor(hmSensor.id.WORLD_CLOCK)
```

## world_clock 实例

### world_clock.init

初始化世界时钟数据

#### 类型

```ts
() => void
```

### world_clock.getWorldClockCount

获取当前配置的世界时钟总数

#### 类型

```ts
() => number
```

### world_clock.getWorldClockCountInfo(index)

获取 `index` 索引的世界时钟数据

#### 类型

```ts
(index: number) => wordInfo
```

##### wordInfo

| 属性           | 说明     | 类型     |
| -------------- | -------- | -------- |
| city           | 城市名   | `string` |
| hour           | 小时     | `number` |
| minute         | 分钟     | `number` |
| timeZoneHour   | 时区小时 | `number` |
| timeZoneMinute | 时区分钟 | `number` |

### world_clock.uninit

回收世界时钟数据，与 `init` 相对应

#### 类型

```js
() => void
```

## 代码示例

```js
const world_clock = hmSensor.createSensor(hmSensor.id.WORLD_CLOCK)
world_clock.init()
const count = world_clock.getWorldClockCount()
if (count > 0) {
  const worldData = world_clock.getWorldClockInfo(0)
  console.log('worldName=' + worldData.city)
  console.log('worldHour=' + worldData.hour)
  console.log('worldMinute=' + worldData.minute)
}
world_clock.uninit()
```
